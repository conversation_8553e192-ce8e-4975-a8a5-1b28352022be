import type {
  ComponentType,
  HTMLElementType,
  HtmlHTMLAttributes,
  PropsWithChildren,
  ReactNode,
} from "react"
import type { ApolloDefineToken, ApolloTheme } from "@apollo/token"

export type ApolloColorTokenKey = keyof ApolloTheme["colors"]
export type ApolloCommonTokenKey = keyof ApolloTheme["tokens"]
export type ApolloTokenKey = ApolloColorTokenKey | ApolloCommonTokenKey
export type ApolloToken = Record<ApolloTokenKey, string>
export type ApolloColorToken = Record<ApolloColorTokenKey, string>
export type ApolloDesignTokenConfig = Record<
  LiteralUnion<ApolloTokenKey>,
  string
>
export type ApolloDesignToken = ApolloToken & ApolloColorToken

type ThemeProviderWithCustomWrapperComponentProps<WrapperComponentProps> = {
  WrapperComponent?: ComponentType<WrapperComponentProps>
} & WrapperComponentProps

type HTMLWrapperProps<WrapperComponentType extends keyof HTMLElementType> =
  HtmlHTMLAttributes<WrapperComponentType>

type ThemeProviderWithHTMLWrapperProps<
  WrapperComponentType extends keyof HTMLElementType,
> = {
  WrapperComponent?: WrapperComponentType
} & HTMLWrapperProps<WrapperComponentType>

export type ThemeProviderProps<WrapperComponentType> =
  ThemeWrapperProps<WrapperComponentType> & {
    children: ReactNode
    theme?: DeepPartial<ApolloDesignTokenConfig>
    scope?: string
    className?: string
  }

export type ThemeWrapperProps<WrapperComponentType> =
  WrapperComponentType extends keyof HTMLElementType
    ? ThemeProviderWithHTMLWrapperProps<WrapperComponentType>
    : ThemeProviderWithCustomWrapperComponentProps<WrapperComponentType>

export type CreateThemeOptions = NullableDeepPartial<ApolloTheme>

/**
 * A type that recursively makes all properties of a type T optional.
 * If T is an object, each property P of T is made optional and also subjected to DeepPartial recursively.
 *
 * Example usage:
 * interface User {
 *   name: string;
 *   address: {
 *     street: string;
 *     city: string;
 *   };
 * }
 *
 * const partialUser: DeepPartial<User> = {
 *   name: 'John',       // optional
 *   address: {          // optional
 *     city: 'New York'  // optional
 *   }
 * };
 */
export type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>
    }
  : T

/**
 * A type that allows T or any string that is not part of the union T.
 * This is useful when you want to allow specific literal values as well as arbitrary strings.
 *
 * Example usage:
 * type Status = 'success' | 'error';
 *
 * function setStatus(status: LiteralUnion<Status>) {
 *   // status can be 'success', 'error', or any other string value.
 * }
 *
 * setStatus('success'); // valid
 * setStatus('error');   // valid
 * setStatus('pending'); // valid
 */
export type LiteralUnion<T extends U, U = string> =
  | T
  | (U & Record<never, never>)

/**
 * A type that recursively makes all properties of a type T optional and nullable.
 * If T is an object, each property P of T is made optional, nullable, and also subjected to NullableDeepPartial recursively.
 *
 * Example usage:
 * interface User {
 *   name: string;
 *   address: {
 *     street: string;
 *     city: string;
 *   };
 * }
 *
 * const partialUser: NullableDeepPartial<User> = {
 *   name: null,         // optional and nullable
 *   address: {          // optional and nullable
 *     city: 'New York'  // optional and nullable
 *   }
 * };
 */
export type NullableDeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: NullableDeepPartial<T[P]> | null
    }
  : T | null

export type ApolloDefineTokenKey = keyof ApolloDefineToken
export type ApolloDefineTokenConfig = Record<
  LiteralUnion<ApolloDefineTokenKey>,
  Record<never, never>
>
export interface ThemeConfig {
  tokens?: Partial<ApolloDefineTokenConfig>
  inherit?: boolean
}

export interface ParentPropsTheme {
  theme: ThemeConfig
  mode: "light" | "dark"
}
export type ThemeProps = PropsWithChildren<{
    theme?: ThemeConfig
    extendedTheme?: ThemeConfig
    mode?: "light" | "dark"
    parentProps?: ParentPropsTheme
  }>
