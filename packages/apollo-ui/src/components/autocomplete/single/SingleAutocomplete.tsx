import { useCallback, useMemo, useState } from "react"
import { Menu } from "@base-ui-components/react/menu"

import { Portal } from "../../portal"
import type {
  AutocompleteOption,
  SingleAutocompleteProps,
} from "../AutocompleteProps"
import { AUTOCOMPLETE_CONSTANTS } from "../constants"
import { useAutocomplete } from "../context/AutocompleteContext"
import { useLabelHeight } from "../hooks/useLabelHeight"
import { SingleAutocompleteMenuPopup } from "./SingleAutocompleteMenuPopup"
import { SingleThumbnailAutocomplete } from "./SingleThumbnailAutocomplete"

export function SingleAutocomplete<ValueType>({
  value,
  onChange,
  menuLabelText,
}: Pick<
  SingleAutocompleteProps<ValueType>,
  "value" | "onChange" | "onSearch" | "menuLabelText"
>) {
  const {
    options,
    fullWidth,
    label,
    helperText,
    error,
    placeholder,
    required,
    loading,
    labelDecorator,
    helperTextDecorator,
    search,
    hasMore,
    disabled,
    loadingMore,
    loadMoreLabel,
    disableSearch,
    filteredOptions,
    loadingComponent,
    noOptionsComponent,
    size = "medium",
    handleSearch,
    handleOpenChange,
    onLoadMore,
    onFocus,
    onBlur,
    // New props
    className,
    style,
    id,
    name,
    autoFocus,
    tabIndex,
    "aria-label": ariaLabel,
    "aria-labelledby": ariaLabelledby,
    "aria-describedby": ariaDescribedby,
    inputProps,
    menuProps,
  } = useAutocomplete<ValueType>()

  const [focusedIndex, setFocusedIndex] = useState<number>(-1)
  const { labelHeight, labelRef } = useLabelHeight(!!label)

  const handleChange = useCallback(
    (
      changingOption: AutocompleteOption<ValueType>,
      event?: React.MouseEvent<HTMLElement>
    ) => {
      onChange?.(
        value === changingOption.value ? undefined : changingOption.value,
        event
      )
    },
    [onChange, value]
  )

  const handleKeyboard = useCallback(
    (props: React.ComponentProps<"div">) =>
      (event: React.KeyboardEvent<HTMLDivElement>) => {
        if (!filteredOptions?.length) return

        if (
          AUTOCOMPLETE_CONSTANTS.KEYBOARD.ALLOWED_KEYS.includes(
            event.code as (typeof AUTOCOMPLETE_CONSTANTS.KEYBOARD.ALLOWED_KEYS)[number]
          )
        ) {
          event.preventDefault()

          if (event.code === "ArrowDown") {
            setFocusedIndex((prev) => (prev + 1) % filteredOptions.length)
          } else if (event.code === "ArrowUp") {
            setFocusedIndex(
              (prev) =>
                (prev - 1 + filteredOptions.length) % filteredOptions.length
            )
          } else if (event.code === "Enter" && focusedIndex >= 0) {
            handleChange(filteredOptions[focusedIndex])
          }

          if (event.type === "keydown") {
            props?.onKeyDown?.(event)
          } else if (event.type === "keyup") {
            props?.onKeyUp?.(event)
          }
        }
      },
    [focusedIndex, handleChange, filteredOptions]
  )

  const handleClear = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation()
      onChange?.(undefined)
    },
    [onChange]
  )

  const selectedOption = useMemo(
    () =>
      value ? options?.find((option) => option.value === value) : undefined,
    [value, options]
  )

  const handleMenuStateChange = useCallback(
    (open: boolean, event?: Event) => {
      if (open) {
        onFocus?.(event)
      } else {
        onBlur?.(event)
      }
      handleOpenChange(open)
    },
    [handleOpenChange, onBlur, onFocus]
  )

  const calculateSideOffset = useCallback(
    (data: { anchor: { height: number }; side: "top" | "bottom" }) =>
      -(data.anchor.height - (data.side === "bottom" ? labelHeight : 0)),
    [labelHeight]
  ) as Menu.Positioner.Props["sideOffset"]

  return (
    <Menu.Root closeParentOnEsc onOpenChange={handleMenuStateChange}>
      <Menu.Trigger
        render={(props, state) => (
          <SingleThumbnailAutocomplete
            open={state.open}
            fullWidth={fullWidth}
            onClick={props?.onClick}
            selectedOption={selectedOption}
            onClear={handleClear}
            label={label}
            helperText={helperText}
            labelDecorator={labelDecorator}
            helperTextDecorator={helperTextDecorator}
            size={size}
            ref={props?.ref}
            error={error}
            required={required}
            placeholder={placeholder}
            disabled={disabled}
            labelRef={labelRef}
            // New props
            className={className}
            style={style}
            id={id}
            name={name}
            autoFocus={autoFocus}
            tabIndex={tabIndex}
            aria-label={ariaLabel}
            aria-labelledby={ariaLabelledby}
            aria-describedby={ariaDescribedby}
            inputProps={inputProps}
          />
        )}
      />
      <Portal baseComponent={<Menu.Portal />}>
          <Menu.Positioner
            positionMethod="fixed"
            sideOffset={calculateSideOffset}
          >
            <SingleAutocompleteMenuPopup
              options={filteredOptions}
              value={value}
              onClear={handleClear}
              onChange={handleChange}
              onSearch={handleSearch}
              onKeyboardEvent={handleKeyboard}
              size={size}
              placeholder={placeholder}
              loading={loading}
              disableSearch={disableSearch}
              search={search}
              onLoadMore={onLoadMore}
              loadingMore={loadingMore}
              loadMoreLabel={loadMoreLabel}
              hasMore={hasMore}
              loadingComponent={loadingComponent}
              noOptionsComponent={noOptionsComponent}
              menuProps={menuProps}
              menuLabelText={menuLabelText}
            />
          </Menu.Positioner>
      </Portal>
    </Menu.Root>
  )
}
