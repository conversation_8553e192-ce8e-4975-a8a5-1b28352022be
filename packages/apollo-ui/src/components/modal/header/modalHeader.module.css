@layer legacy {
    .popupHeader {
        display: flex;
        justify-content: flex-start;
        gap: var(--apl-space-padding-xs);
        align-items: center;
        padding: var(--apl-space-padding-md);
        padding-bottom: var(--apl-space-padding-xs);
        color: var(--apl-colors-content-default);
        position: relative;

    }

    @media screen and (max-width: 768px) {
        .popupHeader {
            padding: var(--apl-space-padding-xs);
            padding-bottom: var(--apl-space-padding-xs);
            flex-direction: column;
        }
    }
}

@layer apollo {
    .popupHeader {
        display: flex;
        align-items: center;
        gap: var(--apl-alias-spacing-padding-padding7, 12px);
        align-self: stretch;
        color: var(--apl-alias-color-background-and-surface-on-surface);
        padding: 0;
    }

    @media screen and (max-width: 768px) {
        .popupHeader {
            flex-direction: column;
        }
    }
}