{"name": "@design-systems/apollo-icons", "version": "1.2.0", "description": "", "main": "dist/cjs/icons-react.js", "main:umd": "dist/umd/icons-react.js", "module": "dist/esm/icons-react.js", "unpkg": "dist/umd/icons-react.min.js", "typings": "dist/icons-react.d.ts", "sideEffects": false, "files": ["dist", "dynamicIconImports.js", "dynamicIconImports.js.map", "dynamicIconImports.d.ts"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf dist && rm -rf ./src/icons/*.ts", "build": "pnpm clean && pnpm build:icons && pnpm build:bundles", "build:icons": "build-icons --output=./src --templateSrc=./scripts/exportTemplate.mjs --withDynamicImports --aliasesFileExtension=.ts  --iconFileExtension=.ts --exportFileName=index.ts", "build:bundles": "rollup -c ./rollup.config.mjs", "build:sandpack": "rm -rf build-sandpack && tsup"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/eslint-parser": "^7.24.1", "@babel/plugin-syntax-import-assertions": "^7.24.1", "apollo-build-icons": "workspace:*", "@testing-library/jest-dom": "^6.4.2", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "jest-serializer-html": "^7.1.0", "rollup": "^4.9.2", "rollup-plugin-dts": "^6.1.0", "typescript": "^4.9.5"}, "dependencies": {"@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "esbuild": "^0.17.19", "rollup-plugin-esbuild": "^6.1.1"}}