import {
  ComponentType,
  createElement,
  forwardRef,
  SVGElementType,
  type ForwardRefExoticComponent,
  type RefAttributes,
  type SVGProps,
} from "react"

import defaultAttributes from "./defaultAttributes"

export type IconCollectionNode = [
  elementName: SVGElementType,
  attrs: Record<string, string>,
][]

export type SVGAttributes = Partial<SVGProps<SVGSVGElement>>
type ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes

export interface IconCollectionProps extends ComponentAttributes {
  size?: string | number
  absoluteStrokeWidth?: boolean
}

export type IconCollection = ForwardRefExoticComponent<IconCollectionProps>

const libName = "ApolloIcons"

const createIcon = (
  iconName: string,
  iconNode: IconCollectionNode
): ComponentType<IconCollectionProps> => {
  const Component = forwardRef<SVGSVGElement, IconCollectionProps>(
    ({ color, size = 24, className = "", children, ...rest }, ref) => {
      return createElement(
        "svg",
        {
          ref,
          ...defaultAttributes,
          width: size,
          height: size,
          className: [className].join(" "),
          key: iconName,
          ...rest,
          color,
        },
        [
          ...iconNode.map(([tag, attrs], i) =>
            createElement(tag, { key: i, ...attrs })
          ),
          ...(Array.isArray(children) ? children : [children]),
        ]
      )
    }
  )

  Component.displayName = `${libName}_${iconName}`

  return Component
}

export default createIcon
