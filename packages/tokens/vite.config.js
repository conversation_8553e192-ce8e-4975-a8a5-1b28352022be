/// <reference types="vitest" />
/// <reference types="vite/client" />

import path from "path"
import { defineConfig } from "vite"

export default defineConfig({
  test: {
    globals: true,
    environment: "jsdom",
  },
  build: {
    outDir: "dist",
    emptyOutDir: true,
    cssCodeSplit: true,
    minify: true,
    lib: {
      name: "design-token",
      fileName: "index",
      entry: path.resolve(__dirname, "src/index.ts"),
      formats: ["umd", "es"],
    },
    rollupOptions: {
      external: ["react", "react-dom"],
      output: {
        assetFileNames: `[name].[ext]`,
        globals: {
          react: "React",
          "react-dom": "ReactDOM",
        },
      },
    },
  },
})
