{"name": "@design-systems/tokens", "version": "0.0.3", "description": "a design tokens with custom transformer to support tailwind format", "main": "dist/index.umd.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "pnpm run build:raw && pnpm run extract:raw && pnpm run build:sd && node scripts/generate-files.js && pnpm run build:final", "build:raw": "pnpm token-transformer src/raw-tokens src/tokens/core-resolved.json", "extract:raw": "pnpm node-jq '.global * .Alias' src/tokens/core-resolved.json > src/tokens/core.json", "build:sd": "style-dictionary build --config src/sd.config.js", "build:sdtw": "node src/sd-tw.config.js", "build:sandpack": "rm -rf build-sandpack && tsup", "build:final": "vite build && tsc"}, "keywords": [], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "ISC", "repository": {"type": "git", "url": "git+ssh://***********************:cjexpress/design-systems/apollo.git"}, "publishConfig": {"@design-systems:registry": "https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/"}, "homepage": "https://gitlab.cjexpress.io/cjexpress/design-systems/apollo#readme", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@tokens-studio/sd-transforms": "^0.12.2", "deepmerge": "^4.3.1", "node-jq": "6.0.1", "sd-tailwindcss-transformer": "^1.4.1", "style-dictionary": "^3.9.1", "token-transformer": "0.0.33", "typescript": "^4.5.2", "vite": "^3.1.3"}}