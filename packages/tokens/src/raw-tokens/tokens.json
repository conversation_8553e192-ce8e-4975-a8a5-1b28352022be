{"global": {"typography": {"H1": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontWeight": {"value": "SemiBold", "type": "fontWeights"}, "fontSize": {"value": "28", "type": "fontSizes"}}, "H2": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontWeight": {"value": "Medium", "type": "fontWeights"}, "fontSize": {"value": "24", "type": "fontSizes"}}, "H3": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontWeight": {"value": "Medium", "type": "fontWeights"}, "fontSize": {"value": "20", "type": "fontSizes"}}, "H4": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontWeight": {"value": "Medium", "type": "fontWeights"}, "fontSize": {"value": "16", "type": "fontSizes"}}, "H5": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontWeight": {"value": "Medium", "type": "fontWeights"}, "fontSize": {"value": "14", "type": "fontSizes"}}, "Body1": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontWeight": {"value": "Regular", "type": "fontWeights"}, "fontSize": {"value": "16", "type": "fontSizes"}}, "Body2": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontWeight": {"value": "Regular", "type": "fontWeights"}, "fontSize": {"value": "14", "type": "fontSizes"}}, "Caption": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontSize": {"value": "12", "type": "fontSizes"}}}, "colors": {"brand": {"10": {"value": "#F5FFF7", "type": "color"}, "20": {"value": "#D5F0E0", "type": "color"}, "30": {"value": "#8EC5A5", "type": "color"}, "40": {"value": "#5FA77D", "type": "color"}, "50": {"value": "#409261", "type": "color"}, "60": {"value": "#006D2E", "type": "color"}, "70": {"value": "#004A1F", "type": "color"}, "80": {"value": "#003C19", "type": "color"}, "90": {"value": "#002C12", "type": "color"}, "95": {"value": "#00210E", "type": "color"}}, "gray": {"0": {"value": "#FFFFFF", "type": "color"}, "5": {"value": "#FAFAFA", "type": "color"}, "10": {"value": "#F6F7FB", "type": "color"}, "20": {"value": "#E9EBF0", "type": "color"}, "30": {"value": "#D3D7E1", "type": "color"}, "40": {"value": "#BEC4D1", "type": "color"}, "50": {"value": "#A8B0C2", "type": "color"}, "60": {"value": "#778093", "type": "color"}, "70": {"value": "#5C6372", "type": "color"}, "80": {"value": "#424752", "type": "color"}, "90": {"value": "#272A31", "type": "color"}, "100": {"value": "#0C0E11", "type": "color"}}, "danger": {"10": {"value": "#FFF2F2", "type": "color"}, "20": {"value": "#F9D1D1", "type": "color"}, "30": {"value": "#F6BABA", "type": "color"}, "40": {"value": "#EA8484", "type": "color"}, "50": {"value": "#E74747", "type": "color"}, "60": {"value": "#E11919", "type": "color"}, "70": {"value": "#B41414", "type": "color"}, "80": {"value": "#870F0F", "type": "color"}, "90": {"value": "#5A0A0A", "type": "color"}, "95": {"value": "#440808", "type": "color"}}, "warning": {"10": {"value": "#FFF8DD", "type": "color"}, "20": {"value": "#FFEEB1", "type": "color"}, "30": {"value": "#FFDD86", "type": "color"}, "40": {"value": "#FFCB59", "type": "color"}, "50": {"value": "#FFB828", "type": "color"}, "60": {"value": "#F9A60C", "type": "color"}, "70": {"value": "#EC9709", "type": "color"}, "80": {"value": "#DF8806", "type": "color"}, "90": {"value": "#D37A03", "type": "color"}, "95": {"value": "#B26000", "type": "color"}}, "success": {"10": {"value": "#ECF6ED", "type": "color"}, "20": {"value": "#C8E6C9", "type": "color"}, "30": {"value": "#B3DBB5", "type": "color"}, "40": {"value": "#8DC891", "type": "color"}, "50": {"value": "#67B66C", "type": "color"}, "60": {"value": "#41A447", "type": "color"}, "70": {"value": "#348339", "type": "color"}, "80": {"value": "#27622B", "type": "color"}, "90": {"value": "#1A421C", "type": "color"}, "95": {"value": "#143115", "type": "color"}}, "process": {"10": {"value": "#EDF0FE", "type": "color"}, "20": {"value": "#DBE1FF", "type": "color"}, "30": {"value": "#B5C1FA", "type": "color"}, "40": {"value": "#91A3F7", "type": "color"}, "50": {"value": "#6C84F5", "type": "color"}, "60": {"value": "#4765F2", "type": "color"}, "70": {"value": "#3951C2", "type": "color"}, "80": {"value": "#2B3D91", "type": "color"}, "90": {"value": "#1C2861", "type": "color"}, "95": {"value": "#151E49", "type": "color"}}}}, "Alias": {"colors": {"surface": {"static": {"ui": {"default": {"value": "{global.colors.gray.0}", "type": "color"}, "hover": {"value": "{global.colors.gray.10}", "type": "color"}, "active": {"value": "{global.colors.gray.20}", "type": "color"}, "disabled": {"value": "{global.colors.gray.10}", "type": "color"}, "primary": {"value": "{global.colors.brand.10}", "type": "color"}, "delete": {"value": "{global.colors.danger.10}", "type": "color"}}, "default1": {"value": "{global.colors.gray.0}", "type": "color"}, "default2": {"value": "{global.colors.gray.10}", "type": "color"}, "default3": {"value": "{global.colors.gray.20}", "type": "color"}, "process": {"default": {"value": "{global.colors.process.10}", "type": "color"}, "hover": {"value": "{global.colors.process.20}", "type": "color"}, "active": {"value": "{global.colors.process.30}", "type": "color"}, "disabled": {"value": "{global.colors.process.10}", "type": "color"}}, "success": {"default": {"value": "{global.colors.brand.20}", "type": "color"}, "hover": {"value": "{global.colors.brand.30}", "type": "color"}, "active": {"value": "{global.colors.brand.40}", "type": "color"}, "disabled": {"value": "{global.colors.brand.10}", "type": "color"}}, "warning": {"default": {"value": "{global.colors.warning.10}", "type": "color"}, "hover": {"value": "{global.colors.warning.20}", "type": "color"}, "active": {"value": "{global.colors.warning.30}", "type": "color"}, "disabled": {"value": "{global.colors.warning.10}", "type": "color"}}, "danger": {"default": {"value": "{global.colors.danger.10}", "type": "color"}, "hover": {"value": "{global.colors.danger.20}", "type": "color"}, "active": {"value": "{global.colors.danger.30}", "type": "color"}, "disabled": {"value": "{global.colors.danger.10}", "type": "color"}}, "default4": {"value": "{global.colors.process.95}", "type": "color"}, "default5": {"value": "{global.colors.gray.5}", "type": "color"}}, "action": {"primary": {"default": {"value": "{global.colors.brand.60}", "type": "color"}, "hover": {"value": "{global.colors.brand.50}", "type": "color"}, "active": {"value": "{global.colors.brand.70}", "type": "color"}, "disabled": {"value": "{global.colors.gray.10}", "type": "color"}}, "delete": {"default": {"value": "{global.colors.danger.50}", "type": "color"}, "hover": {"value": "{global.colors.danger.40}", "type": "color"}, "active": {"value": "{global.colors.danger.70}", "type": "color"}, "disabled": {"value": "{global.colors.gray.10}", "type": "color"}}, "secondary": {"value": "{global.colors.gray.0}", "type": "color"}}}, "content": {"default": {"value": "{global.colors.gray.100}", "type": "color"}, "inversed": {"value": "{global.colors.gray.0}", "type": "color"}, "disabled": {"value": "{global.colors.gray.40}", "type": "color"}, "description": {"value": "{global.colors.gray.70}", "type": "color"}, "placeholder": {"value": "{global.colors.gray.40}", "type": "color"}, "onaction": {"value": "{global.colors.gray.90}", "type": "color"}, "onactioninversed": {"value": "{Alias.colors.content.inversed}", "type": "color"}, "primary": {"default": {"value": "{global.colors.brand.60}", "type": "color"}, "subdued": {"value": "{global.colors.brand.40}", "type": "color"}}, "process": {"default": {"value": "{global.colors.process.60}", "type": "color"}, "subdued": {"value": "{global.colors.process.40}", "type": "color"}}, "success": {"default": {"value": "{global.colors.success.60}", "type": "color"}, "subdued": {"value": "{global.colors.success.40}", "type": "color"}}, "warning": {"default": {"value": "{global.colors.warning.60}", "type": "color"}, "subdued": {"value": "{global.colors.warning.40}", "type": "color"}}, "danger": {"default": {"value": "{global.colors.danger.60}", "type": "color"}, "subdued": {"value": "{global.colors.danger.40}", "type": "color"}}, "subdued": {"value": "{global.colors.gray.50}", "type": "color"}}, "border": {"default": {"value": "{global.colors.gray.30}", "type": "color"}, "disabled": {"value": "{global.colors.gray.40}", "type": "color"}, "subdued": {"value": "{global.colors.gray.5}", "type": "color"}, "accented": {"value": "{global.colors.gray.100}", "type": "color"}, "inversed": {"value": "{global.colors.gray.0}", "type": "color"}, "primary": {"default": {"value": "{global.colors.brand.60}", "type": "color"}, "subdued": {"value": "{global.colors.brand.50}", "type": "color"}, "accented": {"value": "{global.colors.brand.70}", "type": "color"}}, "process": {"default": {"value": "{global.colors.process.50}", "type": "color"}, "subdued": {"value": "{global.colors.process.30}", "type": "color"}, "accented": {"value": "{global.colors.process.70}", "type": "color"}}, "success": {"default": {"value": "{global.colors.success.50}", "type": "color"}, "subdued": {"value": "{global.colors.success.40}", "type": "color"}, "accented": {"value": "{global.colors.success.70}", "type": "color"}}, "warning": {"default": {"value": "{global.colors.warning.50}", "type": "color"}, "subdued": {"value": "{global.colors.warning.40}", "type": "color"}, "accented": {"value": "{global.colors.warning.70}", "type": "color"}}, "danger": {"default": {"value": "{global.colors.danger.50}", "type": "color"}, "subdued": {"value": "{global.colors.danger.40}", "type": "color"}, "accented": {"value": "{global.colors.danger.70}", "type": "color"}}, "focus": {"value": "{global.colors.brand.20}", "type": "color"}}}}, "Component": {"M-H1": {"fontFamily": {"value": "IBM Plex Sans Thai", "type": "fontFamilies"}, "fontWeight": {"value": "SemiBold", "type": "fontWeights"}}}, "$metadata": {"tokenSetOrder": {"0": {"value": "global", "type": "other"}, "1": {"value": "<PERSON><PERSON>", "type": "other"}, "2": {"value": "Component", "type": "other"}}}}