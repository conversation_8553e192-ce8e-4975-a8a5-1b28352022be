{"name": "@design-systems/apollo-ui", "version": "1.13.9", "description": "collection of UI Components", "main": "./src/index.ts", "module": "", "types": "", "exports": {".": {"types": "./dist/index.d.ts", "import": "./src/index.ts", "require": "./dist/index.cjs"}, "./theme.css": "./dist/theme.css", "./theme": {"import": "./dist/theme.js", "require": "./dist/theme.cjs"}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "README.md"], "publishConfig": {"access": "public"}, "dependencies": {"@apollo/core": "workspace:*", "@base-ui-components/react": "1.0.0-alpha.5", "@design-systems/apollo-icons": "workspace:*", "@design-systems/tokens": "workspace:*", "@mui/base": "^5.0.0-beta.30", "@mui/system": "^5.15.9", "@mui/utils": "^5.15.11", "@types/react-transition-group": "^4.4.10", "date-fns": "^2.30.0", "notistack": "^3.0.1", "react-compiler-runtime": "19.0.0-beta-714736e-20250131", "react-datepicker": "^7.3.0", "react-multi-date-picker": "^4.4.1", "react-transition-group": "^4.4.5"}, "scripts": {"dev": "pnpm rollup --watch", "build": "rm -rf dist && pnpm rollup", "copy": "cp ./package.json ./dist && cp ./README.md ./dist", "rollup": "rollup --config --bundleConfigAsCjs", "format:write": "prettier --write \"**/*.{js,ts,tsx}\" --cache", "format:check": "prettier --check \"**/*.{js,ts,tsx}\" --cache", "clean": "rm -rf ./node_modules && rm -rf ./.turbo && rm -rf ./dist", "lint": "eslint ./src --config .eslintrc.js", "lint:fix": "eslint ./src --fix", "test": "jest .", "test-report": "jest --ci --reporters=default --reporters=jest-junit --json --outputFile=jest-results.json"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.20", "@types/react": "^18.2.58", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.1.0", "apollo-build-icons": "workspace:*", "apollo-typescript-config": "workspace:*", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^1.2.1", "deepmerge": "^4.3.1", "eslint": "^8.57.0", "eslint-config-custom": "workspace:*", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "moment": "^2.30.1", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "prop-types": "^15.8.1", "react-element-popper": "^2.1.6", "react-is": "19.0.0", "rollup": "^4.12.0", "rollup-plugin-banner2": "^1.2.2", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-preserve-directives": "^0.4.0", "rollup-plugin-visualizer": "^5.12.0", "tailwind-merge": "^2.2.2", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.2", "tsc-alias": "^1.8.8", "tslib": "^2.6.2", "tsup": "^8.3.5", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^16 || ^17 || ^18", "react-dom": "^16 || ^17 || ^18 || <=19"}}