import { useCallback } from "react"

import { MenuItem, MenuItemGroup } from "../MenuItem"
import type { SidebarMenuListProps } from "./SidebarProps"
import { useInternalSidebar } from "./useInternalSidebar"

export function SidebarMenuList({
  config,
  selectedMenuKey,
  expandedMenuKeys = [],
  onExpandedChange,
  onSelectMenu,
  expandedWhenSelectedSubItem = true,
}: SidebarMenuListProps) {
  const { getMenuState, isSubItemSelected, isSelected, handleClickMenu } =
    useInternalSidebar({
      selectedMenuKey,
      config,
      onSelectMenu,
    })

  const isControlledExpandedState = Boolean(onExpandedChange)
  const isGroup = Boolean(config?.children)

  const allExpandedMenuKeys =
    !isControlledExpandedState &&
    expandedWhenSelectedSubItem &&
    isSubItemSelected
      ? [...expandedMenuKeys, config.key]
      : expandedMenuKeys

  const isExpandedMenu = allExpandedMenuKeys?.includes(config.key)

  const handleExpandStateChange = useCallback(
    (key: string | number) => (state: boolean) => {
      onExpandedChange?.(key, state)
    },
    [onExpandedChange]
  )

  return isGroup ? (
    <MenuItemGroup
      aria-label="apolloSidebar-menuGroup"
      className="ApolloSidebar-menuGroup"
      expanded={isExpandedMenu}
      icon={config.icon}
      key={config.key}
      label={config.label}
      onExpandedStateChange={
        isControlledExpandedState
          ? handleExpandStateChange(config.key)
          : undefined
      }
      selected={isSelected}
    >
      {config?.children?.map((item) => {
        const isSelectedSubItem = item?.selected || getMenuState(item.key)
        return (
          <MenuItem
            {...item}
            aria-label="apolloSidebar-menuItem"
            className="ApolloSidebar-menuItem [&_.ApolloMenuItem-container]:items-start [&_.ApolloMenuItem-icon]:pt-[4px] [&_.ApolloMenuItem-label]:whitespace-normal"
            key={item.key}
            onClick={handleClickMenu(item.key, config.key, item.onClick)}
            selected={isSelectedSubItem}
            subItem
          />
        )
      })}
    </MenuItemGroup>
  ) : (
    <MenuItem
      {...config}
      aria-label="apolloSidebar-menuItem"
      className="ApolloSidebar-menuItem [&_.ApolloMenuItem-container]:items-start [&_.ApolloMenuItem-icon]:pt-[4px] [&_.ApolloMenuItem-label]:whitespace-normal"
      key={config.key}
      onClick={handleClickMenu(config.key, null, config.onClick)}
      selected={isSelected}
    />
  )
}
