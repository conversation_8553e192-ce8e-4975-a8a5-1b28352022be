"use client"

import { isValidElement, useMemo } from "react"
import { Left, Logout, Menu } from "@design-systems/apollo-icons"

import { mergeClass } from "@/utils/helpers"

import { useDynamicState } from "../../hooks/useDynamicState"
import { Drawer } from "../Drawer"
import { IconButton } from "../IconButton"
import { MenuItem } from "../MenuItem"
import { Typography } from "../Typography"
import { SidebarCollapsedMenuList } from "./SidebarCollapsedMenuList"
import { SidebarMenuSection } from "./SidebarMenuSection"
import type { SidebarProps } from "./SidebarProps"
import { getFlattenMenu } from "./utils"

function Sidebar(props: SidebarProps) {
  const {
    title,
    logo,
    header,
    menus,
    footer,
    selectedMenuKey,
    onSelectMenu,
    expandedMenuKeys,
    onExpandedChange,
    width,
    onLogOut,
    logOutButtonLabel = "Log out",
    collapsible,
    collapsed: defaultCollapsed,
    onCollapsedChange: onControlledCollapsedChange,
    ...drawerProps
  } = props

  const { value: collapsed, handleChange: onCollapsedChange } =
    useDynamicState<boolean>({
      onChange: onControlledCollapsedChange,
      value: defaultCollapsed,
    })

  const flattenMenu = getFlattenMenu(menus)
  const displayWidth = collapsed ? "56px" : width
  const baseHeaderSectionProps = {
    "aria-label": "apolloSidebar-header",
    className: mergeClass(
      "ApolloSidebar-headerContainer w-full border-b border-border-default"
    ),
  }

  const renderFooter = useMemo(() => {
    const isConfig = Array.isArray(footer)
    if (isValidElement(footer)) {
      return footer
    } else if (isConfig) {
      const flattenFooterMenu = getFlattenMenu(footer)
      return collapsible && collapsed
        ? flattenFooterMenu?.map((footerItem) => (
            <SidebarCollapsedMenuList
              config={footerItem}
              key={footerItem.key}
            />
          ))
        : footer?.map((footerItem) => (
            <SidebarMenuSection
              key={footerItem.key}
              menu={footerItem}
              showDivider={false}
            />
          ))
    }
    return null
  }, [collapsed, collapsible, footer])

  const isCollapsed = collapsible && collapsed

  return (
    <Drawer
      width={displayWidth}
      {...drawerProps}
      aria-label={drawerProps?.["aria-label"] ?? "apolloSidebar-container"}
      className={mergeClass("ApolloSidebar-container", drawerProps?.className)}
    >
      {header ? (
        <section {...baseHeaderSectionProps}>{header}</section>
      ) : (
        <section
          {...baseHeaderSectionProps}
          className={mergeClass(
            baseHeaderSectionProps.className,
            "flex flex-row justify-between items-center gap-2 p-4",
            {
              "flex-col justify-center items-center": collapsed,
            }
          )}
        >
          <section className="flex flex-row items-center gap-2">
            {logo}
            {title && !collapsed ? (
              <Typography level="h5">{title}</Typography>
            ) : null}
          </section>
          {collapsible ? (
            <IconButton
              onClick={() => onCollapsedChange?.(!collapsed)}
              size="small"
            >
              {collapsed ? <Menu /> : <Left />}
            </IconButton>
          ) : null}
        </section>
      )}
      <section
        aria-label="apolloSidebar-menuSection"
        className={mergeClass(
          "ApolloSidebar-menuContainer flex-1 overflow-auto self-stretch py-4 flex flex-col justify-start items-start h-fit overflow-y-auto gap-2",
          { "hidden-scrollbar items-center": isCollapsed }
        )}
      >
        {isCollapsed
          ? flattenMenu?.map((menu) => (
              <SidebarCollapsedMenuList
                config={menu}
                key={menu.key}
                onSelectMenu={onSelectMenu}
                selectedMenuKey={selectedMenuKey}
              />
            ))
          : menus?.map((menu, index) => (
              <SidebarMenuSection
                expandedMenuKeys={expandedMenuKeys}
                key={menu.key}
                menu={menu}
                onExpandedChange={onExpandedChange}
                onSelectMenu={onSelectMenu}
                selectedMenuKey={selectedMenuKey}
                showDivider={index !== menus.length - 1}
              />
            ))}
      </section>
      {footer ? (
        <section
          aria-label="apolloSidebar-footer"
          className={mergeClass(
            "ApolloSidebar-footerContainer w-full min-h-fit border-t border-border-default",
            {
              "flex flex-col justify-start items-center": isCollapsed,
            }
          )}
        >
          {renderFooter}
          {onLogOut ? (
            <MenuItem
              icon={<Logout />}
              label={logOutButtonLabel}
              onClick={onLogOut}
              onlyIcon={isCollapsed}
            />
          ) : null}
        </section>
      ) : null}
    </Drawer>
  )
}

Sidebar.displayName = "Sidebar"

export default Sidebar
