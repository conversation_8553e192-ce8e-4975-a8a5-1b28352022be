import { ButtonProps as ButtonPropsBase } from "@mui/base/Button"

export type ColorProp =
  | "primary"
  | "danger"

export type VariantProp =
  | "solid"
  | "outline"
  | "plain"

export type SizeProp = "sm" | "md" | "lg"

export type ButtonProps = ButtonPropsBase & {
  /** @default 'primary' */
  color?: ColorProp

  /** @default false */
  disabled?: boolean

  /** @default false */
  fullWidth?: boolean

  /** @default 'md' */
  size?: SizeProp

  /** @default 0 */
  tabIndex?: NonNullable<React.HTMLAttributes<any>["tabIndex"]>

  /** @default 'solid' */
  variant?: VariantProp

  /** @default false */
  loading?: boolean

  /** @default 'start' */
  loadingPosition?: "start" | "end"

  loadingIndicator?: React.ReactNode
  startDecorator?: React.ReactNode
  endDecorator?: React.ReactNode
  as?: React.ElementType
  action?: React.Ref<{ focusVisible(): void }>
}
