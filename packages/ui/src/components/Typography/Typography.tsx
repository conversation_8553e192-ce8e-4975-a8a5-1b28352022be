import { forwardRef } from "react"
import { useSlotProps } from "@mui/base/utils"
import { cva } from "class-variance-authority"

import {
  DefaultTypographySystem,
  TypographyProps,
} from "@/components/Typography/TypographyProps"
import { mergeClass } from "@/utils/helpers"

const typographyVariants = cva("antialiased", {
  variants: {
    level: {
      h1: "text-h1 font-h1",
      h2: "text-h2 font-h2",
      h3: "text-h3 font-h3",
      h4: "text-h4 font-h4",
      h5: "text-h5 font-h5",
      "body-1": "text-body-1 font-body-1",
      "body-2": "text-body-2 font-body-2",
      caption: "text-caption font-caption",
    },
    align: {
      left: "text-start",
      right: "text-end",
      center: "text-center",
      justify: "text-justify",
      inherit: "text-inherit",
    },
    color: {
      primary: "text-surface-action-primary-default",
      danger: "text-content-danger-default",
    },
  },
  defaultVariants: {
    level: "body-1",
    align: "left",
  },
})

const defaultLevelMapping: Record<DefaultTypographySystem, string> = {
  h1: "h1",
  h2: "h2",
  h3: "h3",
  h4: "h4",
  h5: "h5",
  "body-1": "p",
  "body-2": "p",
  caption: "span",
}

const Typography = forwardRef<HTMLElement, TypographyProps>(
  function Typography(props, ref) {
    const {
      gutterBottom = false,
      noWrap = false,
      level = "body-1",
      align = "left",
      endDecorator,
      startDecorator,
      color,
      children,
      className,
      ...other
    } = props

    const Root = (defaultLevelMapping[level] || "span") as React.ElementType

    const externalForwardedProps = { ...other }

    const rootProps = useSlotProps({
      elementType: Root,
      externalForwardedProps,
      className: mergeClass(
        typographyVariants({ level, color, align }),
        className,
        {
          "mb-[0.35em]": gutterBottom,
          "overflow-hidden text-ellipsis whitespace-nowrap": noWrap,
        },
      ),
      externalSlotProps: {},
      ownerState: {},
    })

    return (
      <Root {...rootProps} ref={ref}>
        {startDecorator ? { startDecorator } : null}
        {children}
        {endDecorator ? { endDecorator } : null}
      </Root>
    )
  }
)

Typography.displayName = "Typography"

export default Typography
