import type {
  HTMLAttributes,
  PropsWithChildren,
  ReactElement,
  ReactNode,
  Ref,
} from "react"

import { Override } from "@/utils/helpers"

import { NotificationBadgeProps } from "../NotificationBadge"

export type NavBarMenuItem<MetaData extends {} = {}> = {
  label: string
  icon: ReactNode
  badge?: string
  badgeProps?: Omit<NotificationBadgeProps, "content">
  activeIcon?: ReactNode
  hidden?: boolean
} & MetaData

export type NavBarProps<T extends {} = {}> = Override<
  HTMLAttributes<HTMLElement>,
  {
    menu: NavBarMenuItem<T>[]
    activeIndex?: number
    hideShadow?: boolean
    onChange?: (index: number, menu: NavBarMenuItem<T>) => void
  }
>

export type NavBarComponent = {
  <MetaData extends {}>(
    props: NavBarProps<MetaData>,
    ref?: Ref<HTMLButtonElement>
  ): ReactElement
  displayName?: string
}

export type NavBarItemWrapperProps<T extends boolean> = T extends true
  ? NotificationBadgeProps
  : PropsWithChildren
