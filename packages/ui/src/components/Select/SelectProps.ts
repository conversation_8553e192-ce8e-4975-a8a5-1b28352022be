import type { ReactElement, Ref } from "react"
import type { SelectProps as BaseSelectProps } from "@mui/base/Select"

import type { Override } from "@/utils/helpers"

import type { FormControlProps } from "../FormControl"

type OmittedBaseSelectProps<T extends {}, Multiple extends boolean> = Omit<
  BaseSelectProps<T, Multiple>,
  "color" | "startAdornment" | "endAdornment"
>

type BaseSelectAppearanceProps = {
  /**
   * @default 'outline'
   */
  variant?: VariantProp
  /**
   * @default 'primary'
   */
  color?: ColorProp
}

export type ColorProp = "primary" | "danger"
export type VariantProp = "outline"

export type SelectCustomProps<
  T extends {},
  Multiple extends boolean = false,
> = Override<BaseSelectCustomProps, OmittedBaseSelectProps<T, Multiple>>

export type BaseSelectCustomProps = Override<
  Omit<FormControlProps, "onChange">,
  BaseSelectAppearanceProps
>

/**
 * Purpose: Defines the SelectComponent type to support generic types with dynamic value types.
 * Cause: forwardRef doesn't work well with generic types, so we use type assertions in onChange handlers for type safety.
 *
 * Example Usage:
 * // Single select usage
 * const [singleValue, setSingleValue] = useState<string>('Option1');
 *
 * <Select
 *   value={singleValue}
 *   onChange={(value: string) => setSingleValue(value)} // value: string
 * />;
 *
 * // Multiple select usage
 * const [multipleValues, setMultipleValues] = useState<string[]>(['Option1', 'Option2']);
 *
 * <Select
 *   multiple
 *   value={multipleValues}
 *   onChange={(value: string[]) => setMultipleValues(value)} // value: string[]
 * />;
 */
export type SelectComponent = {
  <TValue extends {}, Multiple extends boolean = false>(
    props: SelectCustomProps<TValue, Multiple>,
    ref?: Ref<HTMLButtonElement>
  ): ReactElement
  displayName?: string
}
