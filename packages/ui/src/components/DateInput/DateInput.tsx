"use client"

import {
  forwardRef,
  use<PERSON><PERSON>back,
  useMemo,
  useRef,
  useState,
  type ChangeEventHandler,
} from "react"
import { Calendar, Left, Right } from "@design-systems/apollo-icons"
import type { InputInputSlotPropsOverrides, InputOwnerState } from "@mui/base"
import { SlotComponentProps } from "@mui/utils"
import {
  getMonth,
  getYear,
  isAfter,
  isBefore,
  isValid,
  setMonth,
  setYear,
  startOfDay,
} from "date-fns"
import { enUS, th } from "date-fns/locale"
import DatePicker from "react-datepicker"

import { mergeClass } from "@/utils/helpers"

import { Button } from "../Button"
import { Input } from "../Input"
import { Typography } from "../Typography"
import type { DateInputProps, DateInputViewMode } from "./DateInputProps"
import { defaultFormatMap, format as formatDate, parse } from "./utils"

const DateInput = forwardRef<DatePicker, DateInputProps>(
  function DateInputRoot(props, ref) {
    const [showCalendar, setShowCalendar] = useState<boolean | undefined>(false)
    const {
      value: selectedDate,
      onChange,
      onBlur,
      label,
      helperText,
      error,
      className,
      placeholder,
      locale: localeProps = "th",
      format,
      inputProps,
      disabled,
      era = "bd",
      excludeDates,
      showMonthYearPicker,
      showYearPicker,
      onViewModeChange,
      startDate,
      isRange,
      endDate,
      hideCalendarMonth,
      hideCalendarYear,
      shouldCloseOnSelect,
      shouldBackToDateViewAfterSelect = true,
      portal,
      ...reactDatePickerProps
    } = props

    const defaultViewMode = useMemo(() => {
      if (showMonthYearPicker) {
        return "month"
      } else if (showYearPicker) {
        return "year"
      }
      return "date"
    }, [showMonthYearPicker, showYearPicker])

    const isBuddhistEra = format ? format?.includes("bb") : era === "bd"
    const displayEra = isBuddhistEra ? "bd" : "ad"
    const defaultFormat = defaultFormatMap?.[displayEra]
    const locale = localeProps === "th" ? th : enUS

    const [typingValue, setTypingValue] = useState<string | null>(null)
    const calendarNavigationFns = useRef({
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      changeMonth: (_: number) => {},
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      changeYear: (_: number) => {},
    })

    const [viewMode, setViewMode] = useState<DateInputViewMode>(defaultViewMode)

    const [isDatePicker, isMonthPicker, isYearPicker] = useMemo(
      () => [viewMode === "date", viewMode === "month", viewMode === "year"],
      [viewMode]
    )

    const handleChangeViewMode = useCallback(
      (mode: DateInputViewMode) => {
        onViewModeChange?.(mode)
        setViewMode(mode)
      },
      [onViewModeChange]
    )

    const getParsedValue = useCallback(
      (selectedDate: Date | null, currentValue?: Date | null) => {
        if (isMonthPicker || isYearPicker) {
          return currentValue ?? null
        }
        return selectedDate
      },
      [isMonthPicker, isYearPicker]
    )

    const validateLimitDate = (date: Date) => {
      if (date && (props?.minDate || props?.maxDate)) {
        if (props?.minDate && isBefore(date, props.minDate)) {
          return props.minDate
        }

        if (props?.maxDate && isAfter(date, props.maxDate)) {
          return props.maxDate
        }
      }
      return date
    }

    const handleChange = (value: Date | null, event: any) => {
      const isWithTimeSelectVisibleOnChange =
        reactDatePickerProps?.showTimeSelect && !event
      if (isWithTimeSelectVisibleOnChange || event.type === "click") {
        const currentValue = selectedDate ?? startOfDay(new Date())
        let newValue: Date | null = value

        if (value) {
          if (isMonthPicker) {
            const newMonth = getMonth(value)
            const newYear = getYear(value)
            newValue = setYear(setMonth(currentValue, newMonth), newYear)

            if (shouldBackToDateViewAfterSelect) {
              handleChangeViewMode("date")
            }
          } else if (isYearPicker) {
            const newYear = getYear(value)
            newValue = setYear(currentValue, newYear)
            if (shouldBackToDateViewAfterSelect) {
              handleChangeViewMode("date")
            }
          }
        }

        if (newValue) {
          newValue = validateLimitDate(newValue)
          calendarNavigationFns.current.changeMonth(getMonth(newValue))
          calendarNavigationFns.current.changeYear(getYear(newValue))
        }

        const onChangeFn = onChange as (value: Date | null) => void
        onChangeFn?.(newValue)
      }
    }

    const handleRangeChange = (
      rangeValue: [Date | null, Date | null],
      event: any
    ) => {
      if (event.type === "click") {
        const [newStartDate, newEndDate] = rangeValue
        const parsedStartDate = getParsedValue(newStartDate, startDate)
        const parsedEndDate = getParsedValue(newEndDate, endDate)

        if (isDatePicker) {
          const onChangeFn = onChange as (
            value: [Date | null, Date | null]
          ) => void
          const validStartDate = parsedStartDate
            ? validateLimitDate(parsedStartDate)
            : parsedStartDate
          const validEndDate = parsedEndDate
            ? validateLimitDate(parsedEndDate)
            : parsedEndDate
          onChangeFn?.([validStartDate, validEndDate])
        } else {
          if (shouldBackToDateViewAfterSelect) {
            handleChangeViewMode("date")
          }
        }
      }
    }

    const displayFormat = useMemo(
      () => format ?? defaultFormat?.fullDate,
      [defaultFormat?.fullDate, format]
    )

    const formatFn = useCallback(
      (date?: Date | null) =>
        date
          ? formatDate(date, displayFormat, {
              locale,
            })
          : "",
      [displayFormat, locale]
    )

    const displayValue = useMemo(() => {
      if (isRange) {
        return `${formatFn(startDate)}${startDate ? " - " : ""}${formatFn(
          endDate
        )}`
      }

      return formatFn(selectedDate)
    }, [endDate, formatFn, isRange, selectedDate, startDate])

    const handleFocus = () => {
      setTypingValue(displayValue)
    }

    const handleBlur = () => {
      setTypingValue(null)
      setShowCalendar(undefined)
      onBlur?.()
    }

    const handleRawChange: ChangeEventHandler<HTMLInputElement> = (event) => {
      const value = event.target.value
      setTypingValue(value)

      if (event.type !== "change") {
        return
      }

      if (isRange) {
        const splittedValues = value?.split("-")?.map((str) => str.trim())
        const rawStartDate = splittedValues?.[0]
        const rawEndDate = splittedValues?.[1]

        const onChangeRangeFN = (startDate: Date, endDate: Date | null) => {
          const onChangeFN = onChange as (value: (Date | null)[]) => void
          onChangeFN([startDate, endDate])

          calendarNavigationFns.current.changeMonth(
            getMonth(endDate ?? startDate)
          )
          calendarNavigationFns.current.changeYear(
            getYear(endDate ?? startDate)
          )
        }

        const startDate = parse(rawStartDate, displayFormat, {
          locale,
        }) as Date

        const endDate = parse(rawEndDate, displayFormat, {
          locale,
        }) as Date

        const isStartDateValid = isValid(startDate)
        const isEndDateValid = isValid(endDate)

        const isEndGreaterThanStart = isAfter(endDate, startDate)

        if (!isStartDateValid && !isEndDateValid) {
          onChange?.([null, null])
        }

        if (isStartDateValid && isEndDateValid && isEndGreaterThanStart) {
          const validStartDate = validateLimitDate(startDate)
          const validEndDate = validateLimitDate(endDate)
          onChangeRangeFN(validStartDate, validEndDate)
        } else if (isValid(startDate)) {
          const validStartDate = validateLimitDate(startDate)
          onChangeRangeFN(validStartDate, null)
        }
      } else {
        const onChangeFN = onChange as (value: Date | null) => void

        let newValue = parse(value, displayFormat, {
          locale,
        })

        const isEmptyValue = ["", null].includes(value)
        if (isEmptyValue) {
          onChangeFN(null)
          return
        }
        const isValueValid = isValid(newValue)

        if (isValueValid) {
          const dateValue = newValue as Date
          const validValue = validateLimitDate(dateValue)

          onChangeFN(validValue)

          calendarNavigationFns.current.changeMonth(getMonth(validValue))
          calendarNavigationFns.current.changeYear(getYear(validValue))
        }
      }
    }

    return (
      <DatePicker
        customInput={
          <Input
            endDecorator={<Calendar size={18} />}
            error={error}
            fullWidth
            helperText={helperText}
            label={label}
            placeholder={placeholder}
            {...inputProps}
            className={mergeClass(
              "ApolloDateInput-inputRoot",
              { "[&_svg]:text-content-description": !disabled },
              inputProps?.className
            )}
            slotProps={{
              input: {
                ...(inputProps as
                  | SlotComponentProps<
                      "input",
                      InputInputSlotPropsOverrides,
                      InputOwnerState
                    >
                  | undefined),
                onFocus: () => {
                  setShowCalendar(true)
                },
                onBlur: handleBlur,
                value: typingValue ?? displayValue,
              },
            }}
          />
        }
        disabled={disabled}
        excludeDates={isDatePicker ? excludeDates : undefined}
        locale={locale}
        onCalendarClose={() => {
          handleChangeViewMode(defaultViewMode)
          handleBlur?.()
        }}
        onChange={isRange ? handleRangeChange : handleChange}
        onChangeRaw={handleRawChange}
        onFocus={handleFocus}
        open={showCalendar}
        placeholderText={placeholder}
        ref={ref}
        showDateSelect
        wrapperClassName={mergeClass("ApolloDateInput-root", className)}
        {...reactDatePickerProps}
        renderCustomHeader={({
          date: displayDate,
          decreaseMonth,
          increaseMonth,
          decreaseYear,
          increaseYear,
          changeMonth,
          changeYear,
          prevMonthButtonDisabled,
          nextMonthButtonDisabled,
        }) => {
          calendarNavigationFns.current.changeMonth = changeMonth
          calendarNavigationFns.current.changeYear = changeYear
          return (
            <div className="ApolloDateInput-calendarHeader relative w-full flex flex-row gap-1 items-center justify-around">
              {!isMonthPicker ? (
                <Button
                  className="ApolloDateInput-prevMonthButton text-content-default"
                  disabled={prevMonthButtonDisabled}
                  onClick={isYearPicker ? decreaseYear : decreaseMonth}
                  variant="plain"
                >
                  <Left size={14} />
                </Button>
              ) : null}
              <div className="flex flex-row justify-center items-center">
                {!hideCalendarMonth ? (
                  <Button
                    className="ApolloDateInput-monthPicker w-fit text-content-default p-0 px-1"
                    onClick={() => {
                      if (isMonthPicker) {
                        handleChangeViewMode(defaultViewMode)
                      } else {
                        handleChangeViewMode("month")
                      }
                    }}
                    variant="plain"
                  >
                    <Typography level="h3">
                      {formatDate(displayDate, defaultFormat?.header?.month, {
                        locale,
                      })}
                      ,
                    </Typography>
                  </Button>
                ) : null}
                {!hideCalendarYear ? (
                  <Button
                    className="ApolloDateInput-yearPicker w-fit text-content-default p-0 px-1"
                    onClick={() => {
                      if (isYearPicker) {
                        handleChangeViewMode(defaultViewMode)
                      } else {
                        handleChangeViewMode("year")
                      }
                    }}
                    variant="plain"
                  >
                    <Typography level="h3">
                      {formatDate(displayDate, defaultFormat?.header?.year, {
                        locale,
                      })}
                    </Typography>
                  </Button>
                ) : null}
              </div>
              {!isMonthPicker ? (
                <Button
                  className="ApolloDateInput-nextMonthButton text-content-default"
                  disabled={nextMonthButtonDisabled}
                  onClick={isYearPicker ? increaseYear : increaseMonth}
                  variant="plain"
                >
                  <Right size={14} />
                </Button>
              ) : null}
            </div>
          )
        }}
        renderDayContents={(_, date) => (
          <Typography
            className="ApolloDateInput-day text-center"
            level="body-2"
          >
            {formatDate(date, "d")}
          </Typography>
        )}
        renderMonthContent={(_, shortMonth) => (
          <Typography
            className="ApolloDateInput-month text-center"
            level="body-2"
          >
            {shortMonth}
          </Typography>
        )}
        renderYearContent={(year) => (
          <Typography
            className="ApolloDateInput-year text-center"
            level="body-2"
          >
            {isBuddhistEra ? year + 543 : year}
          </Typography>
        )}
        selected={isRange ? startDate : selectedDate}
        {...((isRange
          ? {
              selectsRange: true,
            }
          : {}) as any)}
        endDate={endDate}
        inline={false}
        portalId={
          reactDatePickerProps?.portalId ??
          (portal ? "apollo-portal-root" : undefined)
        }
        shouldCloseOnSelect={shouldCloseOnSelect ?? isDatePicker}
        showMonthYearPicker={showMonthYearPicker ?? viewMode === "month"}
        showPopperArrow={false}
        showYearPicker={showYearPicker ?? viewMode === "year"}
        startDate={startDate}
        value={displayValue}
      />
    )
  }
)

DateInput.displayName = "DateInput"

export default DateInput
