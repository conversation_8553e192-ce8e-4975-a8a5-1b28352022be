import { DatePickerProps as ReactDatePickerProps } from "react-datepicker"

import { Override } from "@/utils/helpers"

import { InputProps } from "../Input"

type PickInputProps = "label" | "helperText" | "error" | "placeholder"

export type DateInputEra = "ad" | "bd"
export type DateInputLocale = "en" | "th"
export type SingleDatePickerValueHandler = {
  selectsRange?: never
  selectsMultiple?: never
  onChange?: (
    date: Date | null,
    event?: React.MouseEvent<HTMLElement> | React.KeyboardEvent<HTMLElement>
  ) => void
}

export type DateInputViewMode = "date" | "month" | "year"
export type DateInputProps = Override<
  Omit<
    ReactDatePickerProps,
    | "placeholderText"
    | "value"
    | "selectsRange"
    | "selectsMultiple"
    | "startDate"
    | "endDate"
    | "onBlur"
  >,
  {
    /**
     * @default 'bd'
     */
    era?: DateInputEra
    /**
     * @default 'th'
     */
    locale?: DateInputLocale
    format?: string
    inputProps?: Omit<InputProps.InputProps, PickInputProps>
    value?: Date | null
    startDate?: Date | null
    endDate?: Date | null
    onBlur?: () => void
    onViewModeChange?: (mode: DateInputViewMode) => void
    hideCalendarMonth?: boolean
    hideCalendarYear?: boolean
    shouldBackToDateViewAfterSelect?: boolean
    portal?: boolean
  } & Pick<InputProps.InputProps, PickInputProps> &
    (
      | {
          isRange: true
          onChange?: (value: [Date | null, Date | null]) => void
        }
      | {
          isRange?: never
          onChange?: (value: Date | null) => void
        }
    )
>
