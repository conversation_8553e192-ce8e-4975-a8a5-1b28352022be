import { ComponentPropsWithoutRef } from "react"

import { Override } from "@/utils/helpers"

export type labelPlacementProp = "top" | "left" | "right" | "bottom"

export type CheckboxProps = Override<
  ComponentPropsWithoutRef<"input">,
  {
    label?: string

    /** @default "right" */
    labelPlacement?: labelPlacementProp
    activeLabel?: boolean

    /** @default false */
    indeterminate?: boolean

    slotProps?: {
      root?: {
        className?: string
      }
    }
  }
>
