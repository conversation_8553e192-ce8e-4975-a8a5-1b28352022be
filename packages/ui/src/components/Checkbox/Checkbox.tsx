"use client"

/**
 * @todo This component need to be improve
 */
import { forwardRef, useId, useImperativeHandle, useRef } from "react"
import { cva } from "class-variance-authority"

import { mergeClass } from "@/utils/helpers"

import { CheckboxProps } from "./CheckboxProps"

const CheckboxVariants = cva(
  "w-[16px] h-[16px] before:w-[16px] before:h-[16px] relative before:block before:border before:disabled:outline-none before:rounded-[2.5px] before:border-border-default before:outline-border-focus md:enabled:hover:before:border-border-primary-subdued duration-150 appearance-none md:hover:enabled:checked:before:bg-surface-action-primary-hover checked:bg-surface-action-primary-default checked:md:enabled:hover:bg-surface-action-primary-md:hover rounded-[2.5px] md:focus:outline md:focus:outline-border-focus outline-offset-1 enabled:active:outline-offset-0 transition-all outline-transparent md:focus:outline-2 outline-2 before:bg-surface-static-ui-default disabled:before:bg-surface-static-ui-disabled disabled:checked:after:border-content-disabled disabled:before:border-border-disabled disabled:checked:before:bg-surface-static-ui-disabled",
  {
    variants: {
      indeterminate: {
        default:
          "accent-surface-action-primary-default md:enabled:hover:disabled:before:border-border-default md:enabled:hover:accent-surface-action-primary-md:hover before:md:focus:border-border-primary-subdued before:checked:md:focus:border-none before:checked:border-surface-action-primary-default before:disabled:checked:border-border-default before:checked:md:enabled:hover:border-transparent checked:after:content-[' '] checked:after:absolute checked:after:left-[5px] checked:after:top-[2px] checked:after:w-[6px] checked:after:h-[10px] checked:after:border-r-2 checked:after:border-b-2 checked:after:border-white checked:after:rotate-45 checked:before:border-transparent checked:md:enabled:hover:before:border-transparent checked:before:bg-surface-action-primary-default",
        all: "after:block after:absolute after:checked:disabled:bg-content-disabled before:checked:disabled:bg-surface-static-ui-disabled after:w-[10px] after:h-[10px] after:rounded-[1px] after:top-[3px] after:left-[3px] before:checked:rounded-[2px] after:checked:md:enabled:hover:bg-surface-action-primary-md:hover after:bg-surface-action-primary-default before:checked:border-border-default before:checked:md:focus:border-border-default before:md:focus:border-border-primary-subdued before:md:enabled:hover:border-border-primary-subdued before:checked:md:enabled:hover:border-border-default border-white disabled:after:bg-content-disabled enabled:hover:after:bg-surface-action-primary-hover enabled:hover:before:bg-surface-static-ui-default",
      },
    },
    defaultVariants: {
      indeterminate: "default",
    },
  }
)

const LabelVariants = cva("flex flex-row justify-start items-center gap-2", {
  variants: {
    labelPlacement: {
      top: "flex-col-reverse",
      bottom: "flex-col",
      right: "",
      left: "flex-row-reverse",
    },
  },
  defaultVariants: {
    labelPlacement: "right",
  },
})

const Checkbox = forwardRef<HTMLInputElement | undefined, CheckboxProps>(
  function Checkbox(props, ref) {
    const {
      id,
      value,
      label,
      labelPlacement = "right",
      disabled,
      required,
      defaultChecked,
      checked,
      activeLabel,
      indeterminate = false,
      onChange = () => {},
      className,
      slotProps,
      ...other
    } = props

    const inputRef = useRef<HTMLInputElement>(null)

    const defaultCheckboxID = useId()
    const checkboxID = id
      ? id
      : `__ApolloCheckbox-autogen-id_${defaultCheckboxID}`

    useImperativeHandle(ref, () => {
      if (inputRef.current) {
        inputRef.current.indeterminate = indeterminate

        return inputRef.current
      }
    }, [indeterminate])

    return (
      <div
        className={mergeClass(
          LabelVariants({ labelPlacement }),
          slotProps?.root?.className
        )}
      >
        <input
          checked={checked}
          className={mergeClass(
            CheckboxVariants({
              indeterminate: indeterminate ? "all" : "default",
            }),
            className,
            "ApolloCheckbox-root"
          )}
          defaultChecked={defaultChecked}
          disabled={disabled}
          id={checkboxID}
          onChange={onChange}
          ref={inputRef}
          type="checkbox"
          value={value}
          {...other}
        />
        <div className="text-start">
          <label
            className={mergeClass(
              "ApolloCheckbox-label",
              "text-body-2 font-body-2",
              `${disabled && !activeLabel ? "text-content-disabled" : "text-content-default"}`
            )}
            htmlFor={checkboxID}
          >
            {label}
            {required ? "*" : null}
          </label>
        </div>
      </div>
    )
  }
)

Checkbox.displayName = "Checkbox"

export default Checkbox
