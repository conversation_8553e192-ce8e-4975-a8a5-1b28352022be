import { InputProps as BaseInputProps } from "@mui/base/Input"

import type { Override } from "@/utils/helpers"

export type ColorProp = "primary" | "danger"

export type VariantProp = "outline"

export type InputProps = Override<
  Omit<BaseInputProps, "startAdornment" | "endAdornment">,
  {
    size?: "medium" | "small"
    disabled?: boolean
    variant?: VariantProp
    color?: ColorProp
    fullWidth?: boolean
    startDecorator?: BaseInputProps["startAdornment"]
    endDecorator?: BaseInputProps["endAdornment"]
    label?: string
    helperText?: string
    maxLength?: number
    minLength?: number
    pattern?: string
  }
>
