import { forwardRef, type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react"
import { useInput, useSlotProps, type FormControlState } from "@mui/base"
import { cva } from "class-variance-authority"

import { mergeClass } from "@/utils/helpers"

import { FormControl } from "../FormControl"
import type { InputProps } from "./InputProps"

export const inputVariants = cva(
  [
    "text-body-1 font-body-1 leading-none",
    "relative flex justify-center items-center gap-2 px-2 rounded-md bg-surface-static-ui-default",
    "placeholder:text-muted-foreground placeholder:disabled:text-content-description",
    "file:border-0 file:bg-transparent file:text-sm file:font-medium",
  ],
  {
    variants: {
      variant: {
        outline: "border transition-colors shadow-none",
      },
      color: {
        primary:
          "border-border-default enabled:hover:border-border-primary-subdued",
        danger: "",
      },
      width: {
        default: "w-auto",
        full: "w-full",
      },
      size: {
        medium: "px-2 py-2",
        small: "px-2 py-1",
      },
    },
    compoundVariants: [
      {
        variant: "outline",
        color: "danger",
        className: "border-border-danger-default",
      },
      {
        variant: "outline",
        color: "primary",
        className:
          "focus-within:border-border-primary-default enabled:active:border-border-primary-default",
      },
    ],
    defaultVariants: {
      variant: "outline",
      color: "primary",
      width: "default",
      size: "medium",
    },
  }
)

const Input = forwardRef<HTMLDivElement, InputProps>(
  function InputRoot(props, ref) {
    const {
      "aria-describedby": ariaDescribedby,
      "aria-label": ariaLabel,
      "aria-labelledby": ariaLabelledby,
      autoComplete,
      autoFocus,
      className,
      variant,
      color,
      defaultValue,
      disabled,
      startDecorator,
      endDecorator,
      error,
      id,
      multiline = false,
      fullWidth = false,
      name,
      onClick,
      onChange,
      onKeyDown,
      onKeyUp,
      onFocus,
      onBlur,
      placeholder,
      readOnly,
      required,
      value,
      type: typeProp,
      rows,
      slotProps = {},
      slots = {},
      minRows,
      maxRows,
      label,
      helperText,
      size = "medium",
      inputRef,
      minLength,
      maxLength,
      pattern,
      ...other
    } = props

    const {
      getRootProps,
      getInputProps,
      focused,
      formControlContext,
      error: errorState,
      disabled: disabledState,
    } = useInput({
      disabled,
      defaultValue,
      error,
      onBlur,
      onClick,
      onChange,
      onFocus,
      value,
      inputRef,
    })

    const type = !multiline ? (typeProp ?? "text") : undefined

    const ownerState: any = {
      ...props,
      disabled: disabledState,
      error: errorState,
      focused,
      formControlContext,
      multiline,
      type,
    }

    const Root = slots.root ?? "div"
    const rootProps: any = useSlotProps({
      elementType: Root,
      getSlotProps: getRootProps,
      externalSlotProps: slotProps.root,
      externalForwardedProps: other,
      additionalProps: { ref },
      ownerState,
      className: mergeClass(
        "ApolloInput-root",
        inputVariants({
          size,
          variant,
          color: errorState ? "danger" : color,
          width: fullWidth ? "full" : "default",
        }),
        { "text-content-description bg-surface-static-ui-disabled": disabled },
        { "p-0 [&_textarea]:px-2 [&_textarea]:py-2": multiline },
        { "[&_textarea]:min-h-[68px]": multiline && size === "medium" },
        { "[&_textarea]:min-h-[56px]": multiline && size === "small" },
        className
      ),
    })

    const InputComponent = multiline
      ? (slots.textarea ?? "textarea")
      : (slots.input ?? "input")

    const propsToForward = {
      "aria-describedby": ariaDescribedby,
      "aria-label": ariaLabel,
      "aria-labelledby": ariaLabelledby,
      autoComplete,
      autoFocus,
      id,
      onKeyDown,
      onKeyUp,
      name,
      placeholder,
      readOnly,
      type,
      disabled,
    }

    const inputProps: any = useSlotProps({
      elementType: InputComponent,
      getSlotProps: (otherHandlers: any) => {
        return getInputProps({
          ...propsToForward,
          ...otherHandlers,
        })
      },
      externalSlotProps: slotProps.input,
      additionalProps: {
        rows: multiline ? rows : undefined,
        ...(multiline &&
          !(typeof InputComponent === "string") && {
            minRows: rows || minRows,
            maxRows: rows || maxRows,
          }),
      },
      ownerState,
      className:
        "ApolloInput-input w-full bg-transparent focus-visible:border-transparent focus-visible:outline-none",
    })

    const handleBlur: FocusEventHandler<HTMLInputElement> = (event) => {
      inputProps?.onBlur(event)
      onBlur?.(event)
    }

    const handleFocus: FocusEventHandler<HTMLInputElement> = (event) => {
      inputProps?.onFocus(event)
      onFocus?.(event)
    }

    const baseDecoratorClassName =
      "w-fit h-full flex justify-center items-center text-content-description"

    return (
      <FormControl
        className={mergeClass("ApolloInput-formControl", {
          "[&_.ApolloFormControl-label]:text-content-default": disabled,
        })}
        disabled={disabled}
        error={error}
        fullWidth={fullWidth}
        helperText={helperText}
        label={label}
        required={required}
      >
        {(formState: FormControlState) => (
          <Root {...rootProps}>
            {startDecorator ? (
              <span
                className={mergeClass(
                  "ApolloInput-startDecorator",
                  baseDecoratorClassName,
                  {
                    "text-content-disabled": disabled,
                  }
                )}
              >
                {startDecorator}
              </span>
            ) : null}
            <InputComponent
              {...inputProps}
              disabled={formState?.disabled}
              maxLength={maxLength}
              minLength={minLength}
              onBlur={handleBlur}
              onFocus={handleFocus}
              pattern={pattern}
            />
            {endDecorator ? (
              <span
                className={mergeClass(
                  "ApolloInput-endDecorator",
                  baseDecoratorClassName,
                  {
                    "text-content-disabled": disabled,
                  }
                )}
              >
                {endDecorator}
              </span>
            ) : null}
          </Root>
        )}
      </FormControl>
    )
  }
)

Input.displayName = "Input"

export default Input
