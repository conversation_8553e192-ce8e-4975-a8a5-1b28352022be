import type { PropsWithChildren, ReactNode } from "react"
import type { FormControlProps as BaseFormControlProps } from "@mui/base/FormControl"

import type { Override } from "@/utils/helpers"

type OmittedBaseFormControlProps = Omit<
  BaseFormControlProps,
  "onChange" | "value"
>
type FormControlAppearanceProps = {
  label?: string
  helperText?: ReactNode
  fullWidth?: boolean
}

export type FormControlProps = Override<
  OmittedBaseFormControlProps,
  FormControlAppearanceProps
>

export type CustomFormControlRootProps = PropsWithChildren<{
  className?: string
  fullWidth?: boolean
}>
