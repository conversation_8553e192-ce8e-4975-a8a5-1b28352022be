import { forwardRef } from "react"
import { FormControl as BaseFormControl, FormControlState } from "@mui/base"

import { mergeClass } from "@/utils/helpers"

import { Typography } from "../Typography"
import CustomFormControlRoot from "./CustomFormControlRoot"
import type { FormControlProps } from "./FormControlProps"

const FormControl = forwardRef<HTMLDivElement, FormControlProps>(
  function FormControlRoot(props, ref) {
    const {
      label,
      error,
      helperText,
      children,
      className,
      fullWidth,
      ...formControlProps
    } = props

    return (
      <BaseFormControl
        {...formControlProps}
        className={mergeClass("gap-1", className)}
        ref={ref}
        slotProps={{
          root: (ownerState) => ({
            ownerState,
            fullWidth,
            className,
          }),
        }}
        slots={{
          root: CustomFormControlRoot,
        }}
      >
        {(formControlState: FormControlState) => (
          <>
            {label ? (
              <Typography
                className={mergeClass(
                  "ApolloFormControl-label text-content-onaction w-full"
                )}
                level="caption"
              >
                {label}
                {formControlState?.required ? (
                  <span className="ApolloFormControl-requiredAsterisk text-content-danger-default">
                    *
                  </span>
                ) : null}
              </Typography>
            ) : null}
            {typeof children === "function"
              ? children(formControlState)
              : children}
            {typeof helperText === "string" ? (
              <Typography
                className={mergeClass(
                  "ApolloFormControl-helperText w-full",
                  error
                    ? "text-content-danger-default"
                    : "text-content-description"
                )}
                level="caption"
              >
                {helperText}
              </Typography>
            ) : (
              helperText
            )}
          </>
        )}
      </BaseFormControl>
    )
  }
)

FormControl.displayName = "FormControl"

export default FormControl
