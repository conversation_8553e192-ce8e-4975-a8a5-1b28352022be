import { mergeClass } from "@/utils/helpers"

import { Typography } from "../Typography"
import type { MenuItemBaseContentProps } from "./MenuItemProps"

export function MenuItemBaseContent({
  label,
  icon,
  selected,
  endDecorator,
  menuGroup,
  onlyIcon,
}: MenuItemBaseContentProps) {
  return (
    <div
      className={mergeClass(
        "ApolloMenuItem-container w-full flex flex-row justify-start items-center gap-2 text-content-description",
        {
          "text-content-primary-default": selected,
        },
        {
          "justify-center": onlyIcon,
        },
        { "w-[86%]": menuGroup }
      )}
    >
      <div className="ApolloMenuItem-icon min-w-[16px] min-h-[16px] [&_svg]:w-[16px] [&_svg]:h-[16px]">
        {icon}
      </div>
      {!onlyIcon && (
        <>
          <Typography
            className="ApolloMenuItem-label flex-1 text-left text-ellipsis overflow-hidden whitespace-nowrap"
            level="body-1"
          >
            {label}
          </Typography>
          {endDecorator}
        </>
      )}
    </div>
  )
}
