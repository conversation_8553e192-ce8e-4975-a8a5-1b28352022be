import type { ReactNode } from "react"
import type { ModalProps as BaseModalProps } from "@mui/base"

import type { Override } from "@/utils/helpers"

export type ModalSize = "default" | "full"
export type ModalProps = Override<
  BaseModalProps,
  {
    /**
     * @default "default"
     */
    size?: ModalSize
    minWidth?: string
    maxWidth?: string
    scrollableContent?: boolean
    /**
     * @default true
     */
    closeAfterPressEsc?: boolean
    /**
     * @default true
     */
    closeAfterClickBackdrop?: boolean
    onClose: () => void
    icon?: ReactNode
    header?: ReactNode
    footer?: ReactNode
    /**
     * @default false
     */
    hideCloseIcon?: boolean
    onOk?: () => void
    disabledOkButton?: boolean
    okButtonText?: string
    onCancel?: () => void
    disabledCancelButton?: boolean
    cancelButtonText?: string
    onDelete?: () => void
    disabledDeleteButton?: boolean
    deleteButtonText?: string
  }
>

export type NegativeModalProps = Omit<
  ModalProps,
  "onOk" | "icon" | "onDelete" | "deleteButtonText" | "disabledDeleteButton"
> & {
  onConfirm?: () => void
  /**
   * @default 'Confirm'
   */
  confirmButtonText?: string
  disabledConfirmButton?: boolean
}
