import { forwardRef, useCallback, useEffect, useRef, useState } from "react"
import { useGetClosetThemeStyleElement } from "@/hooks"
import { Close } from "@design-systems/apollo-icons"
import { Modal as BaseModal } from "@mui/base"

import { mergeClass } from "@/utils/helpers"

import { Button } from "../Button"
import { IconButton } from "../IconButton"
import { Typography } from "../Typography"
import ModalBackdrop from "./ModalBackdrop"
import type { ModalProps } from "./ModalProps"

const Modal = forwardRef<HTMLDivElement, ModalProps>(function ModalRoot(
  {
    header,
    footer,
    icon,
    children,
    okButtonText,
    disabledOkButton,
    onOk,
    onClose,
    cancelButtonText,
    disabledCancelButton,
    onCancel,
    deleteButtonText,
    disabledDeleteButton,
    onDelete,
    minWidth,
    maxWidth,
    scrollableContent = false,
    size = "default",
    closeAfterPressEsc = true,
    closeAfterClickBackdrop = true,
    hideCloseIcon = false,
    ...props
  },
  ref
) {
  const [currentModalElementWrapper, setCurrentModalElementWrapper] =
    useState<HTMLElement | null>(null)
  const [contentMaxHeight, setContentMaxHeight] =
    useState<string>("calc(100vh-200px)")

  const headerRef = useRef<HTMLDivElement>(null)
  const footerRef = useRef<HTMLDivElement>(null)
  const modalContentRef = useRef<HTMLDivElement>(null)

  const isTextHeader = typeof header === "string"
  const hasFooter = footer || onOk || onCancel || onDelete

  const closestScopeTheme = useGetClosetThemeStyleElement(
    currentModalElementWrapper,
    !props?.disablePortal
  )

  const calculateContentMaxHeight = useCallback(() => {
    if (!scrollableContent) return

    // Early return if refs are not ready
    if (!headerRef.current && hasFooter && !footerRef.current) return

    const headerHeight = headerRef.current?.offsetHeight || 0
    const footerHeight = hasFooter ? footerRef.current?.offsetHeight || 0 : 0
    const modalPadding = 64 // py-4 = 16px top + 16px bottom
    const bufferSpace = 40 // Extra buffer for safety

    const totalUsedSpace =
      headerHeight + footerHeight + modalPadding + bufferSpace
    const maxHeight = `calc(100vh - ${totalUsedSpace}px)`

    // Only update if the value actually changed
    setContentMaxHeight((prev) => (prev === maxHeight ? prev : maxHeight))
  }, [scrollableContent, hasFooter])

  const handleClose = useCallback(
    (_: {}, reason = "closeButton") => {
      const isClickCloseButton = reason === "closeButton"
      const isCloseAfterPressESC =
        reason === "escapeKeyDown" && closeAfterPressEsc
      const isCloseAfterClickBackdrop =
        reason === "backdropClick" && closeAfterClickBackdrop

      if (
        isClickCloseButton ||
        isCloseAfterClickBackdrop ||
        isCloseAfterPressESC
      ) {
        onClose?.()
      }
    },
    [onClose, closeAfterPressEsc, closeAfterClickBackdrop]
  )

  useEffect(() => {
    calculateContentMaxHeight()

    // Debounced resize handler for better performance - only on height changes
    let resizeTimeout: ReturnType<typeof setTimeout>
    let previousHeight = window.innerHeight

    const handleResize = () => {
      const currentHeight = window.innerHeight

      // Only recalculate if window height actually changed
      if (currentHeight !== previousHeight) {
        clearTimeout(resizeTimeout)
        resizeTimeout = setTimeout(() => {
          calculateContentMaxHeight()
          previousHeight = currentHeight
        }, 150)
      }
    }

    window.addEventListener("resize", handleResize, { passive: true })

    return () => {
      window.removeEventListener("resize", handleResize)
      clearTimeout(resizeTimeout)
    }
  }, [calculateContentMaxHeight])

  // Recalculate when modal content changes (debounced)
  useEffect(() => {
    const timer = setTimeout(calculateContentMaxHeight, 200)
    return () => clearTimeout(timer)
  }, [header, footer, hasFooter, calculateContentMaxHeight])

  return (
    <>
      {!currentModalElementWrapper && (
        <div
          ref={(positionElementRef) => {
            if (!currentModalElementWrapper && positionElementRef) {
              setCurrentModalElementWrapper(positionElementRef)
            }
          }}
        />
      )}
      <BaseModal
        {...props}
        className={mergeClass(
          "Apollo-Modal-root",
          "fixed flex flex-col justify-center items-center w-full h-full top-0 z-[400]",
          "overflow-y-auto max-h-[100vh]",

          { "px-6": size === "default" },
          props?.className
        )}
        onClose={handleClose}
        ref={ref}
        role="dialog"
        slots={{
          backdrop: ModalBackdrop,
        }}
      >
        <div
          className={mergeClass(
            "Apollo-Modal-contentContainer",
            "flex flex-row gap-2 justify-start items-start shadow-lg rounded-2xl focus:outline-none w-full",
            size === "full"
              ? "w-full rounded-none"
              : "max-w-[400px] min-h-fit"
          )}
          data-cjx={
            closestScopeTheme
              ? closestScopeTheme?.getAttribute("data-cjx-wrapper-id")
              : undefined
          }
          style={{
            minWidth: minWidth,
            maxWidth: maxWidth,
          }}
        >
          {!props?.disablePortal && closestScopeTheme !== null ? (
            <style
              dangerouslySetInnerHTML={{
                __html: closestScopeTheme.innerHTML,
              }}
            />
          ) : null}
          <div
            className={mergeClass(
              "Apollo-Modal-content",
              "w-full flex-1 flex flex-col justify-start items-start relative self-stretch h-fit bg-surface-static-ui-default rounded-2xl py-4"
            )}
            ref={modalContentRef}
          >
            <div
              className={mergeClass(
                "Apollo-Modal-content-mainHeader",
                "w-full flex flex-row justify-between items-start px-6"
              )}
            >
              <div
                className={mergeClass(
                  "Apollo-Modal-headerContainer",
                  "w-full flex flex-row justify-start items-start mb-2 gap-2",
                  "max-md:justify-center max-md:items-start",
                )}
                ref={headerRef}
              >
                {icon ? (
                  <div className="Apollo-Modal-icon w-fit py-2">{icon}</div>
                ) : null}
                {isTextHeader ? (
                  <Typography
                    className="line-clamp-2 break-words leading-[33px]"
                    level="h3"
                  >
                    {header}
                  </Typography>
                ) : (
                  header
                )}
              </div>
              {!hideCloseIcon && (
                <IconButton
                  className="Apollo-Modal-closeButton w-fit h-fit text-content-default p-1 mr-[-8px]"
                  onClick={handleClose}
                  variant="plain"
                >
                  <Close className="!w-[16px] !h-[16px]"/>
                </IconButton>
              )}
            </div>
            <div
              className={mergeClass(
                "Apollo-Modal-contentWrapper px-6 self-stretch flex-1",
                {
                  "overflow-y-auto": scrollableContent,
                }
              )}
              style={{
                maxHeight: scrollableContent ? contentMaxHeight : undefined,
                scrollbarWidth: "thin",
                scrollbarColor:
                  "var(--cjx-colors-content-description) transparent",
              }}
            >
              {children}
            </div>
            {hasFooter ? (
              <div
                className="Apollo-Modal-footerContainer px-6 mt-6 self-stretch flex flex-row justify-end items-center gap-2 max-md:w-full max-md:flex-col-reverse"
                ref={footerRef}
              >
                {footer ?? (
                  <>
                    {onCancel ? (
                      <Button
                        className="max-md:w-full"
                        disabled={disabledCancelButton}
                        onClick={onCancel}
                        variant="outline"
                      >
                        {cancelButtonText ?? "Cancel"}
                      </Button>
                    ) : null}
                    {onDelete ? (
                      <Button
                        className="max-md:w-full"
                        color="danger"
                        disabled={disabledDeleteButton}
                        onClick={onDelete}
                        variant="solid"
                      >
                        {deleteButtonText ?? "Delete"}
                      </Button>
                    ) : onOk ? (
                      <Button
                        className="max-md:w-full"
                        disabled={disabledOkButton}
                        onClick={onOk}
                        variant="solid"
                      >
                        {okButtonText ?? "Ok"}
                      </Button>
                    ) : null}
                  </>
                )}
              </div>
            ) : null}
          </div>
        </div>
      </BaseModal>
    </>
  )
})

Modal.displayName = "Modal"

export default Modal
