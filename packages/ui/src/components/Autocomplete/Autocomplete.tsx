"use client"

import {
  forwardRef,
  useCallback,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
  type HTMLAttributes,
  type <PERSON><PERSON><PERSON>Hand<PERSON>,
  type Ref,
} from "react"
import { Close, Down, Up } from "@design-systems/apollo-icons"
import {
  Unstable_Popup as Popup,
  useAutocomplete,
  type AutocompleteValue,
} from "@mui/base"

import { getBooleanishValue, mergeClass } from "@/utils/helpers"

import { Button } from "../Button"
import { Chip } from "../Chip"
import { Input } from "../Input"
import ScrollTriggerElement from "../ScrollTriggerElement/ScrollTriggerElement"
import { Typography } from "../Typography"
import AutocompleteMenuItem from "./AutocompleteMenuItem"
import type {
  AutocompleteComponent,
  AutocompleteOption,
  AutocompleteProps,
} from "./AutocompleteProps"
import AutocompleteTagWrapper from "./AutocompleteTagWrapper"
import { observeElementResize } from "./utils"

const DEFAULT_MIN_WIDTH = 128
const DEFAULT_MIN_HEIGHT = 44

function AutocompleteRoot<
  ValueType extends AutocompleteOption,
  Multiple extends boolean = false,
  DisableClearable extends boolean = false,
>(
  props: AutocompleteProps<ValueType, Multiple, DisableClearable>,
  ref: Ref<HTMLInputElement>
) {
  const [inputValue, setInputValue] = useState("")
  const [menuContainerRef, setMenuContainerRef] =
    useState<HTMLUListElement | null>(null)
  const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null)

  const [tagWrapperRef, setTagWrapperRef] = useState<HTMLDivElement | null>()
  const [tagWrapperDimension, setTagWrapperDimension] = useState<{
    height: number
    width: number
  }>({ height: DEFAULT_MIN_HEIGHT, width: DEFAULT_MIN_WIDTH })

  const {
    id,
    inputProps,
    label,
    loadMoreLabel,
    helperText,
    fullWidth,
    value,
    onChange,
    onFocus,
    onBlur,
    options,
    multiple,
    disablePortal,
    limitTags: externalLimitTags,
    className,
    error,
    placeholder,
    disabled,
    hasSelectAll,
    onClickAllOption,
    allOption,
    loading,
    hasLoadMore,
    onLoadMore,
    required,
    isClearSearchKeywordAfterSelect,
    inputValue: externalInputValue,
    onInputChange: externalOnInputChange,
    disableClearable,
    noItemLabel = "No item",
    slots,
    slotProps,
    inputRef,
    popupProps,
    hideOverflowTag = true,
    searchable = true,
    ...autoCompleteBaseProps
  } = props

  const defaultOnInputChange: AutocompleteProps["onInputChange"] =
    !isClearSearchKeywordAfterSelect
      ? (_, value, reason) => {
          if (multiple) {
            if (reason !== "reset") {
              setInputValue(value)
            } else if (reason === "reset" && !focused) {
              setInputValue("")
            }
          } else {
            setInputValue(value)
          }
        }
      : undefined
  const handleExternalInputValueChange: AutocompleteProps["onInputChange"] = (
    event,
    value,
    reason
  ) => externalOnInputChange?.(event, value, reason, focused)

  const {
    getRootProps,
    getInputProps,
    getListboxProps,
    getOptionProps,
    groupedOptions,
    focused,
    getTagProps,
    popupOpen,
    getClearProps,
  } = useAutocomplete<ValueType, Multiple, DisableClearable>({
    options,
    value,
    onChange,
    multiple,
    disabled,
    clearOnEscape: false,
    openOnFocus: true,
    blurOnSelect: !multiple,
    disableCloseOnSelect: multiple,
    inputValue: externalInputValue ?? inputValue,
    onInputChange: externalOnInputChange
      ? handleExternalInputValueChange
      : defaultOnInputChange,
    getOptionDisabled: (option) => option?.disabled ?? false,
    getOptionLabel: (option) => option.label,
    ...autoCompleteBaseProps,
  })

  const tagRefs = useRef<Record<string, HTMLDivElement | null>>({})

  const [isMeasuring, setIsMeasuring] = useState(hideOverflowTag)
  const [maxVisibleTag, setMaxVisibleTag] = useState<number>()

  const baseClearProps = getClearProps()
  const baseRootProps = getRootProps()
  const baseInputProps = getInputProps()

  const multipleValues = useMemo(
    () => (multiple ? (isMeasuring ? options : (value as ValueType[])) : []),
    [multiple, isMeasuring, options, value]
  )
  const hasValue = Boolean(multiple ? multipleValues.length : value)
  const isFoundOptions = groupedOptions.length > 0

  const limitTags =
    hideOverflowTag && externalLimitTags && maxVisibleTag
      ? Math.min(externalLimitTags, maxVisibleTag)
      : externalLimitTags

  const tags = useMemo(() => {
    if (multiple) {
      const isHiddenOverflowLimitTag = !focused && limitTags
      return multipleValues?.filter((_, index) =>
        isHiddenOverflowLimitTag ? index + 1 <= limitTags : true
      )
    }
    return []
  }, [focused, limitTags, multiple, multipleValues])

  const hasTag = useMemo(
    () => (multiple ? Array.isArray(value) && tags.length > 0 : false),
    [multiple, tags.length, value]
  )

  const hiddenTags = useMemo(() => {
    if (!multiple) {
      return 0
    }

    const isHideExceedLimitTags = hasTag && limitTags
    return isHideExceedLimitTags ? multipleValues.length - limitTags : 0
  }, [hasTag, limitTags, multiple, multipleValues.length])

  const allFilteredOptions = (groupedOptions as ValueType[])?.filter(
    (options) => !options?.disabled
  )
  const notInTheListOptions = multipleValues?.filter(
    (option) => !allFilteredOptions.some((o) => o.value === option.value)
  )
  const disabledOptions = allFilteredOptions?.filter(
    (option) => option?.disabled
  )
  const selectedDisableOptions = disabledOptions?.filter((option) =>
    multipleValues?.some((o) => o.value === option.value)
  )

  const isShowClearButton = focused && !disableClearable && hasValue

  const enableOptions = useMemo(
    () =>
      (groupedOptions as ValueType[])?.filter((option) => !option?.disabled),
    [groupedOptions]
  )
  const isSelectedAll = useMemo(() => {
    return (
      enableOptions.length > 0 &&
      enableOptions?.every((option) => {
        const currentOptionValue = option as ValueType
        return multipleValues.some(
          (option) => option.value === currentOptionValue.value
        )
      })
    )
  }, [enableOptions, multipleValues])

  const isAllOptionIndeterminate = useMemo(() => {
    const isSelectedSomeOfVisibleOptions = enableOptions?.some((option) => {
      const currentOption = option as ValueType
      return multipleValues.some(
        (option) => option.value === currentOption.value
      )
    })
    return !isSelectedAll && isSelectedSomeOfVisibleOptions
  }, [enableOptions, isSelectedAll, multipleValues])

  const handleClickAll: MouseEventHandler<HTMLLIElement> = useCallback(
    (event) => {
      const allOptions = (options as ValueType[]).filter((option) =>
        [
          ...notInTheListOptions,
          ...allFilteredOptions,
          ...selectedDisableOptions,
        ].some((o) => o.value === option.value)
      ) as AutocompleteValue<ValueType, Multiple, DisableClearable, false>

      const emptyValues = [
        ...notInTheListOptions,
        ...selectedDisableOptions,
      ] as ValueType[] as AutocompleteValue<
        ValueType,
        Multiple,
        DisableClearable,
        false
      >
      if (onClickAllOption) {
        onClickAllOption?.(event, allOptions, allOption?.state ?? "default")
      } else {
        if (isAllOptionIndeterminate || isSelectedAll) {
          onChange?.(event, emptyValues, "clear")
        } else {
          onChange?.(event, allOptions, "selectOption")
        }
      }
    },
    [
      allFilteredOptions,
      allOption?.state,
      isAllOptionIndeterminate,
      isSelectedAll,
      notInTheListOptions,
      onChange,
      onClickAllOption,
      options,
      selectedDisableOptions,
    ]
  )

  const handleClickArrow =
    (action: "blur" | "focus"): MouseEventHandler =>
    (event) => {
      event.stopPropagation()
      const input = rootRef?.querySelector(
        ".ApolloInput-input"
      ) as HTMLInputElement | null
      if (input) {
        if (action === "blur") {
          input.blur()
        } else {
          input.focus()
        }
      }
    }

  const mergeRef = (baseAttributes: any, extendingAttributes: any) => {
    const baseRef = baseAttributes?.ref as
      | { current: HTMLElement | null }
      | undefined
    const extendingRef = extendingAttributes?.ref as
      | { current: HTMLElement | null }
      | undefined
    return {
      ...baseAttributes,
      ...extendingAttributes,
      ref: (instance: HTMLDivElement | HTMLInputElement | null) => {
        if (baseRef) {
          baseRef.current = instance
        }
        if (extendingRef) {
          extendingRef.current = instance
        }
      },
    }
  }

  const rootSlotProps = {
    ...mergeRef(baseRootProps, slotProps?.root),
    className: mergeClass(
      "ApolloAutocomplete-inputRoot flex-wrap gap-1 relative pr-[80px]",
      baseRootProps?.className
    ),
  }

  const inputSlotProps = {
    ...slotProps?.input,
    ...mergeRef(baseInputProps, slotProps?.input),
    className: mergeClass(
      "ApolloAutocomplete-input w-[0px] min-w-[30px] flex-1 text-ellipsis",
      baseInputProps?.className
    ),
  }

  const isReadonly = inputProps?.readOnly ?? !searchable
  const isNotShowPlaceholder =
    multiple && isReadonly && (value as ValueType[]).length > 0
  const inputPlaceholder = isNotShowPlaceholder ? "" : placeholder

  const handleTagRefs = (key: string) => (ref: HTMLDivElement | null) => {
    tagRefs.current[key] = ref
  }

  const isHiddenOverflowLimitTag = useMemo(
    () => multiple && hideOverflowTag,
    [multiple, hideOverflowTag]
  )

  const chipElements = useMemo(
    () =>
      multiple && hasTag ? (
        <AutocompleteTagWrapper
          hasWrapper={Boolean(limitTags && hasTag && !focused)}
          hiddenTags={hiddenTags}
          isMeasuring={isMeasuring}
          ref={setTagWrapperRef}
        >
          {tags.map((item, index) => {
            const tagProps = getTagProps({ index })
            return (
              <Chip
                {...tagProps}
                className="ApolloAutocomplete-chip min-w-[50px] w-fit"
                color="primary"
                disabled={item?.disabled || disabled}
                key={item.value}
                label={item.label}
                ref={handleTagRefs(item.value)}
              />
            )
          })}
        </AutocompleteTagWrapper>
      ) : undefined,
    [
      multiple,
      hasTag,
      limitTags,
      focused,
      hiddenTags,
      isMeasuring,
      tags,
      getTagProps,
      disabled,
    ]
  )

  useLayoutEffect(() => {
    if (tagWrapperRef) {
      const inputContainer = tagWrapperRef?.parentElement?.parentElement
      const measureSize = () => {
        const width = tagWrapperRef?.getBoundingClientRect().width
        const height = tagWrapperRef?.getBoundingClientRect().height

        setIsMeasuring(false)

        setTagWrapperDimension({
          height: height ?? DEFAULT_MIN_HEIGHT,
          width: width ?? DEFAULT_MIN_WIDTH,
        })

        if (isHiddenOverflowLimitTag) {
          if (inputContainer) {
            const { width: containerWidth, height: containerHeight } =
              inputContainer?.getBoundingClientRect() ?? {
                width: 0,
                height: 0,
              }

            const tagDimensions = Object.entries(tagRefs.current).reduce(
              (allTags, [key, ref]) => ({
                ...allTags,
                [key]: {
                  width: ref?.getBoundingClientRect().width,
                  height: ref?.getBoundingClientRect().height,
                },
              }),
              {} as Record<string, { width?: number; height?: number }>
            )

            const selectedItems = Object.entries(tagDimensions)
              .filter(([key]) =>
                multipleValues.some((item) => item.value === key)
              )
              ?.map(([, value]) => value)

            const containerDimension = {
              width: containerWidth - containerWidth * 0.2,
              height: containerHeight,
            }
            const adjsutedContainerWidth = containerDimension?.width ?? 0

            const result = selectedItems?.reduce(
              ([sum, currentIndex, isFoundIndex], dimension, index) => {
                if (isFoundIndex || sum >= adjsutedContainerWidth) {
                  return [sum, currentIndex, 1]
                }

                const newWidth = sum + (dimension?.width ?? 0)
                return [
                  newWidth,
                  index,
                  newWidth >= adjsutedContainerWidth ? 1 : 0,
                ]
              },
              [0, 0, 0]
            )

            const [allWidth, visibleIndex] = result

            const isExceed = adjsutedContainerWidth <= allWidth
            if (visibleIndex > 0 && isExceed) {
              setMaxVisibleTag(visibleIndex)
            } else {
              setMaxVisibleTag(undefined)
            }
          }
        }
      }

      measureSize()
      let cleanup = () => {}
      if (inputContainer) {
        cleanup = observeElementResize(inputContainer, () => {
          measureSize()
        })
      }

      return () => {
        cleanup()
      }
    }
  }, [isHiddenOverflowLimitTag, multipleValues, tagWrapperRef])

  return (
    <div
      className={mergeClass(
        "ApolloAutocomplete-root relative h-fit max-w-full",
        fullWidth ? "w-full" : "w-fit min-w-[320px]",
        className
      )}
      id={id}
      ref={ref}
    >
      <Input
        {...inputProps}
        className={mergeClass(
          "flex-1 flex flex-row items-center flex-wrap justify-start max-h-[256px] overflow-y-auto",
          "[&_.ApolloInput-startDecorator]:flex-wrap",
          {
            "flex-nowrap": limitTags && !focused,
            "[&_.ApolloInput-input]:opacity-0 [&_.ApolloInput-input]:h-0":
              multiple && hasTag && isReadonly,
          },
          {
            "[&_.ApolloInput-input]:cursor-default": isReadonly,
          }
        )}
        disabled={disabled}
        endDecorator={
          <div
            className="absolute top-0 right-0 overflow-visible"
            style={{
              height: multiple
                ? `${Math.max(tagWrapperDimension.height * 0.9, 30)}px`
                : "100%",
            }}
          >
            <div className="w-fit ApolloAutocomplete-actionContainer sticky h-[0px] top-[50%] left-0 flex flex-row gap-2 justify-end items-center px-4">
              {isShowClearButton ? (
                <Button
                  className="ApolloAutocomplete-clearAllButton w-fit px-2 text-content-default"
                  onClick={baseClearProps?.onClick}
                  variant="plain"
                >
                  <Close size={16} />
                </Button>
              ) : null}
              {popupOpen ? (
                <Up
                  className="ApolloAutocomplete-arrowIcon"
                  onClick={handleClickArrow("blur")}
                  size={16}
                />
              ) : (
                <Down
                  className="ApolloAutocomplete-arrowIcon"
                  onClick={handleClickArrow("focus")}
                  size={16}
                />
              )}
            </div>
          </div>
        }
        error={error}
        fullWidth
        helperText={helperText}
        inputRef={inputRef}
        label={label}
        onBlur={onBlur ?? inputProps?.onBlur}
        onFocus={onFocus ?? inputProps?.onFocus}
        placeholder={inputPlaceholder}
        readOnly={isReadonly}
        ref={setRootRef}
        required={required}
        slotProps={{
          root: rootSlotProps,
          input: inputSlotProps,
        }}
        slots={slots}
        startDecorator={isMeasuring ? undefined : chipElements}
      />
      <Popup
        anchor={rootRef}
        disablePortal={disablePortal}
        offset={8}
        open={popupOpen}
        style={{
          width: rootRef?.getBoundingClientRect()?.width,
        }}
        {...popupProps}
        className={mergeClass(
          "ApolloAutocomplete-popupRoot w-full z-[401]",
          popupProps?.className
        )}
      >
        <ul
          className="ApolloAutocomplete-popupContainer w-full bg-surface-static-ui-default shadow-lg rounded-xl max-h-[328px] overflow-y-auto px-[2px] gap-1 flex flex-col justify-start items-center"
          ref={setMenuContainerRef}
          {...getListboxProps()}
        >
          {isFoundOptions ? (
            <>
              {multiple && hasSelectAll ? (
                <AutocompleteMenuItem
                  hasCheckbox
                  isIndeterminate={
                    allOption?.state
                      ? allOption.state === "indeterminate"
                      : isAllOptionIndeterminate
                  }
                  isSelected={
                    allOption?.state
                      ? allOption.state === "selected"
                      : isSelectedAll
                  }
                  label={allOption?.label ?? "All"}
                  onClick={handleClickAll}
                />
              ) : null}
              {(groupedOptions as ValueType[]).map((option, index) => {
                const {
                  onChange,
                  onMouseDown,
                  onTouchStart,
                  onClick: baseOnClick,
                  onMouseMove,
                  ...baseOptionProps
                } = getOptionProps({ option, index })
                const isSelected = getBooleanishValue(
                  baseOptionProps?.["aria-selected"]
                )
                const isDisabled = getBooleanishValue(
                  baseOptionProps?.["aria-disabled"]
                )

                const onClick: MouseEventHandler<HTMLLIElement> = (event) => {
                  if (option?.onClick) {
                    option.onClick(event, option)
                  } else {
                    baseOnClick?.(event)
                  }
                }

                const actionProps = isDisabled
                  ? {}
                  : {
                      onChange,
                      onMouseDown,
                      onTouchStart,
                      onClick,
                      onMouseMove,
                    }

                const { key: itemKey, ...baseProps } =
                  baseOptionProps as HTMLAttributes<HTMLLIElement> & {
                    key: string
                  }

                return (
                  <AutocompleteMenuItem
                    key={itemKey}
                    {...baseProps}
                    {...actionProps}
                    className={mergeClass(
                      { "pl-6": hasSelectAll },
                      option?.className
                    )}
                    hasCheckbox={multiple}
                    isDisabled={isDisabled}
                    isSelected={isSelected}
                    label={option.label}
                  />
                )
              })}
              {hasLoadMore ? (
                <ScrollTriggerElement
                  className="ApolloAutocomplete-loadingItem"
                  label={loadMoreLabel}
                  onVisible={onLoadMore}
                  parentRef={menuContainerRef}
                />
              ) : null}
            </>
          ) : loading || hasLoadMore ? (
            <ScrollTriggerElement
              className="ApolloAutocomplete-loadingItem"
              label={loadMoreLabel}
              onVisible={onLoadMore}
              parentRef={menuContainerRef}
            />
          ) : (
            <Typography
              className="ApolloAutocomplete-noItem text-content-description  px-4 py-2"
              level="body-2"
            >
              {noItemLabel}
            </Typography>
          )}
        </ul>
      </Popup>
      {isMeasuring ? chipElements : null}
    </div>
  )
}

const Autocomplete = forwardRef(AutocompleteRoot) as AutocompleteComponent
Autocomplete.displayName = "Autocomplete"

export default Autocomplete
