import type { ReactElement, MouseEvent as React<PERSON>ouse<PERSON><PERSON>, Ref } from "react"
import type {
  AutocompleteInputChangeReason,
  AutocompleteValue,
  PopupProps,
  UseAutocompleteProps,
} from "@mui/base"

import type { InputProps } from "../Input/InputProps"

export type AutocompleteOption<ValueType = any> = {
  label: string
  value: ValueType
  disabled?: boolean
  className?: string
  onClick?: (
    event: ReactMouseEvent<HTMLLIElement, MouseEvent>,
    item: ValueType
  ) => void
}

export type AutocompleteBaseProps<
  ValueType,
  Multiple extends boolean,
  DisableClearable extends boolean,
> = Omit<
  UseAutocompleteProps<ValueType, Multiple, DisableClearable, false>,
  "onInputChange"
>

export type AutocompleteAllOption = {
  label?: string
  state?: AutocompleteAllOptionState
}
export type AutocompleteAllOptionState =
  | "selected"
  | "default"
  | "indeterminate"

export type AutocompleteProps<
  ValueType = any,
  Multiple extends boolean = false,
  DisableClearable extends boolean = true,
> = {
  id?: string
  loading?: boolean
  required?: boolean
  noItemLabel?: string
  loadMoreLabel?: string
  hasLoadMore?: boolean
  allOption?: AutocompleteAllOption
  hasSelectAll?: boolean
  disabled?: boolean
  limitTags?: number
  disablePortal?: boolean
  inputProps?: Omit<InputProps, "onChange">
  isClearSearchKeywordAfterSelect?: boolean
  searchable?: boolean
  hideOverflowTag?: boolean
  popupProps?: PopupProps
  onLoadMore?: () => void
  onInputChange?: (
    event: React.SyntheticEvent,
    value: string,
    reason: AutocompleteInputChangeReason,
    focused?: boolean
  ) => void
  onClickAllOption?: (
    event: ReactMouseEvent<HTMLLIElement, MouseEvent>,
    currentValue: AutocompleteValue<
      ValueType,
      Multiple,
      DisableClearable,
      false
    >,
    currentState?: AutocompleteAllOptionState
  ) => void
} & AutocompleteBaseProps<ValueType, Multiple, DisableClearable> &
  Pick<
    InputProps,
    | "label"
    | "helperText"
    | "error"
    | "placeholder"
    | "fullWidth"
    | "className"
    | "slots"
    | "slotProps"
    | "inputRef"
    | "onFocus"
    | "onBlur"
  >

export type AutocompleteComponent = {
  <
    TValue extends {},
    Multiple extends boolean = false,
    DisableClearable extends boolean = false,
  >(
    props: AutocompleteProps<TValue, Multiple, DisableClearable>,
    ref?: Ref<HTMLDivElement>
  ): ReactElement
  displayName?: string
}
