import type { ReactElement, ReactNode, Ref } from "react"

import type { FormControlProps } from "../FormControl"

export type UploadBoxFileType<
  Multiple extends boolean = false,
  FileType = any,
> = Multiple extends true ? FileType[] : FileType
export type UploadBoxBaseFileType = {
  name: string
}
export type UploadBoxFile<
  Multiple extends boolean = false,
  FileType extends { name: string } = UploadBoxBaseFileType,
> = UploadBoxFileType<Multiple, FileType>

export type UploadBoxErrorState = {
  code: string
  message: string
}
export type UploadBoxState = {
  errors: UploadBoxErrorState[]
}

export type UploadBoxFileState = {
  key: string
  uploading?: boolean
  errorMessage?: string
}

export type UploadBoxProps<Multiple extends boolean> = {
  fileState?:
    | (Multiple extends true ? UploadBoxFileState[] : UploadBoxFileState)
    | null
  onCancelUpload?: (file: UploadBoxFile<false>, index: number) => void
  renderDescription?: (state: UploadBoxState) => ReactNode
  renderErrorMessage?: (state: UploadBoxState) => ReactNode
  value?: UploadBoxFile<Multiple, UploadBoxBaseFileType> | null
  multiple?: Multiple
  errorMessage?: string
  /**
   * @default ['jpg','png','svg']
   */
  allowedFilesExtension?: string[]
  /**
   * @default 5*1024*1024 // 5MB
   */
  maxFileSizeInBytes?: number
  /**
   * @default 6
   */
  fileLimit?: number
  onDelete?: Multiple extends true ? (fileIndex: number) => void : () => void
  onUpload?: (files: UploadBoxFileType<Multiple, File>) => void
} & FormControlProps

export type UploadBoxComponent = {
  <Multiple extends boolean = false>(
    props: UploadBoxProps<Multiple>,
    ref?: Ref<HTMLDivElement>
  ): ReactElement
  displayName?: string
}
