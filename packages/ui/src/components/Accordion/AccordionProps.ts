import type { ComponentPropsWithoutRef, ReactNode } from "react"

export type AccordionProps = Omit<
  ComponentPropsWithoutRef<"details">,
  "onChange"
> & {
  header: ReactNode
  /** @default undefined */
  icon?: ReactNode
  /** @default true */
  expanded?: boolean
  /** @default undefined */
  disabled?: boolean
  /** @default undefined */
  onStateChange?: (expanded: boolean) => void

  /** @default 'right' */
  iconPosition?: "start" | "end"
  /** @default undefined */
  borderless?: boolean
  /** @default "default" */
  variant?: "default" | "error"
  /** @default false */
  hasDivider?: boolean
}
