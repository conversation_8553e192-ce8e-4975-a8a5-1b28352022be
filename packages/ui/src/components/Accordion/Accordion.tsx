"use client"

import {
  forwardRef,
  Mouse<PERSON>vent<PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useMemo,
  useState,
} from "react"
import { Down } from "@design-systems/apollo-icons"

import { mergeClass } from "@/utils/helpers"

import { Typography } from "../Typography"
import type { AccordionProps } from "./AccordionProps"

const Accordion = forwardRef<HTMLDetailsElement, AccordionProps>(
  function Accordion(
    {
      header,
      icon,
      children,
      className,
      disabled,
      onStateChange,
      borderless,
      hasDivider,
      expanded: defaultIsExpanded = true,
      iconPosition = "end",
      variant = "default",
      ...containerProps
    }: AccordionProps,
    ref
  ) {
    const [isExpanded, setIsExpanded] = useState(defaultIsExpanded)
    const isBorderless = borderless || hasDivider

    const expanded = useMemo(() => {
      if (disabled) {
        return false
      } else if (onStateChange) {
        return defaultIsExpanded
      } else {
        return isExpanded
      }
    }, [disabled, onStateChange, defaultIsExpanded, isExpanded])

    const handleClick: MouseEventHandler = useCallback(
      (event) => {
        event.preventDefault()
        if (onStateChange) {
          onStateChange?.(!defaultIsExpanded)
        } else {
          setIsExpanded((prev) => !prev)
        }
      },
      [defaultIsExpanded, onStateChange]
    )

    const ariaLabel = containerProps?.["aria-label"] ?? "apolloAccordion"

    const defaultIcon = useMemo(
      () =>
        icon ?? (
          <Down
            aria-label={`${ariaLabel}-headerIcon`}
            className={mergeClass(
              "ApolloAccordion-icon text-content-description w-4 h-4",
              {
                "rotate-180": expanded,
                "text-content-disabled": disabled,
              }
            )}
          />
        ),
      [ariaLabel, disabled, expanded, icon]
    )

    return (
      <details
        {...containerProps}
        className={mergeClass(
          "ApolloAccordion-root",
          "w-full bg-surface-static-ui-default rounded-lg border border-border-default relative overflow-hidden",
          { "border-0 rounded-none": isBorderless },
          { "bg-surface-static-danger-default": variant === "error" },
          className
        )}
        open={expanded}
        ref={ref}
      >
        <summary
          aria-disabled={disabled}
          aria-label={`${ariaLabel}-header`}
          className={mergeClass(
            "ApolloAccordion-header",
            "self-stretch flex flex-row justify-between items-center px-4 py-3 cursor-pointer gap-2 rounded-lg overflow-hidden",
            { "rounded-b-none": isExpanded },
            { "bg-surface-static-ui-disabled cursor-default": disabled },
            { "hover:bg-surface-static-ui-hover": variant === "default" },
            { "rounded-none": isBorderless }
          )}
          onClick={handleClick}
        >
          {iconPosition === "start" ? defaultIcon : null}
          {typeof header === "string" ? (
            <Typography
              aria-label={`${ariaLabel}-headerTitle`}
              className={mergeClass(
                "text-content-default whitespace-nowrap flex-1 text-ellipsis w-full overflow-hidden",
                {
                  "text-content-disabled": disabled,
                }
              )}
              level="h4"
            >
              {header}
            </Typography>
          ) : (
            header
          )}
          {iconPosition === "end" ? defaultIcon : null}
          {hasDivider && (
            <div className="flex absolute left-0 bottom-0 w-full bg-border-default h-[1px]" />
          )}
        </summary>
        {!disabled && (
          <section
            aria-label={`${ariaLabel}-body`}
            className={mergeClass(
              "ApolloAccordion-body",
              "self-stretch text-content-default",
              "h-fit px-4 py-3",
              { "overflow-hidden": !expanded }
            )}
          >
            {children}
          </section>
        )}
      </details>
    )
  }
)

Accordion.displayName = "Accordion"

export default Accordion
