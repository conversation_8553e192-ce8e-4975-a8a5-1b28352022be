"use client"

import { forwardRef, useRef } from "react"
import { useTab } from "@mui/base"
import { Tab as BaseTab } from "@mui/base/Tab"
import useForkRef from "@mui/utils/useForkRef"
import { cva } from "class-variance-authority"

import { Typography } from "@/components/Typography"
import { isTextElement, mergeClass } from "@/utils/helpers"

import { TabProps } from "./TabProps"

export const tabVariants = cva(
  [
    "typography-h4 font-[inherit] leading-[26.4px]",
    "cursor-pointer text-content-description bg-transparent border-b border-border-primary-subdued",
    "md:hover:bg-surface-static-ui-primary",
    "min-w-[80px] py-2 px-4 max-w-[440px]",
    "flex items-end justify-center focus:outline-0",
  ],
  {
    variants: {
      variant: {
        fill: "flex-1",
        fit: "",
      },
    },
    defaultVariants: {
      variant: "fill",
    },
  }
)

const Tab = forwardRef<HTMLButtonElement, TabProps>(
  function TabsList(props, ref) {
    const { children, variant, disabled, className, ...otherTabProps } = props

    const buttonRef = useRef<HTMLElement>(null)
    const handleRef = useForkRef(buttonRef, ref)

    const { rootRef, selected } = useTab({
      ...props,
      rootRef: handleRef,
    })

    return (
      <BaseTab
        ref={rootRef}
        slotProps={{
          root: {
            className: mergeClass(
              tabVariants({
                variant,
              }),
              {
                "text-content-primary-default border-b-4 border-border-primary-default pb-[7px]":
                  selected,
              },
              {
                "cursor-default opacity-50 md:hover:bg-transparent": disabled,
              },
              className
            ),
          },
        }}
        {...otherTabProps}
      >
        {isTextElement(children) ? (
          <Typography className="w-full line-clamp-2 text-center" level="h4" align="center">
            {children}
          </Typography>
        ) : (
          <span className="w-full line-clamp-2 text-center">{children}</span>
        )}
      </BaseTab>
    )
  }
)

Tab.displayName = "Tab"
export default Tab
