import type {
  ComponentType,
  HTMLAttributes,
  ImgHTMLAttributes,
  ReactElement,
  ReactNode,
  Ref,
} from "react"

import type { Override } from "@/utils/helpers"

export type ProductCardProps<
  ImageProps extends {} = ImgHTMLAttributes<HTMLImageElement>,
> = Override<
  HTMLAttributes<HTMLDivElement>,
  {
    /**
     * @default "160px"
     */
    size?: string | "fill"
    extra?: ReactNode
    title: ReactNode
    body?: ReactNode
    footer?: ReactNode
    imageSrc?: string
    imageProps?: Partial<ImageProps>
    noImage?: ReactNode
    /**
     * @default "img"
     */
    ImageComponent?: ComponentType<ImageProps> | "img"
    imageOverlay?: ReactNode
  }
>

export type ProductCardComponent = {
  <ImageProps extends {} = HTMLAttributes<HTMLImageElement>>(
    props: ProductCardProps<ImageProps>,
    ref?: Ref<HTMLDivElement>
  ): ReactElement
  displayName?: string
}
