import { apolloTailwindConfig, typographyVariant } from "@design-systems/tokens"
import merge from "deepmerge"
import type { Config } from "tailwindcss"

import { overrideReactMultiDatePickerStyles } from "../internal/tw-datepicker-override"
import { customUtilityClasses } from "../internal/tw-utility-classes"
import { get } from "./helpers"

const { colors, typography } = apolloTailwindConfig

// from tailwind/createPlugin.js
function createPlugin(plugin: any, config?: any) {
  return {
    handler: plugin,
    config,
  }
}

createPlugin.withOptions = function (
  pluginFunction: (opt: any) => {},
  configFunction = (opt: any) => ({})
) {
  const optionsFunction = function (options: any) {
    return {
      __options: options,
      handler: pluginFunction(options),
      config: configFunction(options),
    }
  }

  optionsFunction.__isOptionsFunction = true

  // Expose plugin dependencies so that `object-hash` returns a different
  // value if anything here changes, to ensure a rebuild is triggered.
  optionsFunction.__pluginFunction = pluginFunction
  optionsFunction.__configFunction = configFunction

  return optionsFunction
}

const materialTailwindConfig: Config = {
  darkMode: "class",
  content: [
    "./node_modules/@design-systems/apollo-ui/dist/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors,
      fontSize: {
        h1: typography["h1"]["fontSize"],
        h2: typography["h2"]["fontSize"],
        h3: typography["h3"]["fontSize"],
        h4: typography["h4"]["fontSize"],
        h5: typography["h5"]["fontSize"],
        "body-1": typography["body1"]["fontSize"],
        "body-2": typography["body2"]["fontSize"],
        caption: typography["caption"]["fontSize"],
      },
      fontWeight: {
        h1: String(typography["h1"]["fontWeight"]),
        h2: String(typography["h2"]["fontWeight"]),
        h3: String(typography["h3"]["fontWeight"]),
        h4: String(typography["h4"]["fontWeight"]),
        h5: String(typography["h5"]["fontWeight"]),
        "body-1": String(typography["body1"]["fontWeight"]),
        "body-2": String(typography["body2"]["fontWeight"]),
        caption: String(get(typography, "caption.fontWeight", 400)),
      },
      aria: {
        invalid: 'invalid="true"',
      },
    },
  },
  plugins: [
    // addDefaultFontFamily(),
    createPlugin(function ({ addComponents }: any) {
      addComponents([
        overrideReactMultiDatePickerStyles,
        customUtilityClasses,
        typographyVariant,
      ])
    }),
  ],
}

/**
 * @param {object} tailwindConfig - Tailwind config object
 * @return {object} new config object
 */
export function withApollo(tailwindConfig: Config) {
  const config = merge(materialTailwindConfig, { ...tailwindConfig })
  return config
}
