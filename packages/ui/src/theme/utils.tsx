import {
  apolloTheme,
  createStyleProperties,
  type ApolloTheme,
} from "@design-systems/tokens"

import { overrideExistedKey } from "@/utils/helpers"

import type { ApolloDesignTokenConfig, CreateThemeOptions } from "./types"

const COLOR_SCHEME_KEY = "prefers-color-scheme"

export function initializePrefersColorScheme() {
  const injectingScript = `(function(){
    var matchedMedia = window.matchMedia('(prefers-color-scheme: dark)')
    var prefersColorScheme = localStorage.getItem('${COLOR_SCHEME_KEY}')
    if (!prefersColorScheme) {
      var colorScheme = matchedMedia.matches ? 'dark' : 'light'
      localStorage.setItem('${COLOR_SCHEME_KEY}', colorScheme)
    }
  })()
  `
  return (
    <script
      dangerouslySetInnerHTML={{
        __html: injectingScript,
      }}
      data-theme="cjx-apollo-color-scheme-injector"
    />
  )
}

export function parseToken(theme: ApolloDesignTokenConfig) {
  return createStyleProperties(theme, true).join("")
}

export function getInjectorId(scope = ":root") {
  return `${scope.replace(/[^a-zA-Z0-9-]/, "")}`
}

export function getThemeWrapperIdentityId(id: string) {
  return `${id}-wrapper-identity`
}

export function createTheme(theme: CreateThemeOptions = {}): ApolloTheme {
  return overrideExistedKey(theme, apolloTheme)
}
