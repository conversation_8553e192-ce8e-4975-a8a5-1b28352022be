import { useMemo, type ComponentType, type ReactHTML } from "react"
import { apolloTheme } from "@design-systems/tokens"

import { animationStyles } from "./override/animation"
import { reactDatePickerStyleOverride } from "./override/react-datepicker"
import { zIndexStyles } from "./override/zIndex"
import { ApolloDesignTokenConfig, ThemeProviderProps } from "./types"
import { getInjectorId, getThemeWrapperIdentityId, parseToken } from "./utils"

const defaultTheme = apolloTheme

const baseTheme: ApolloDesignTokenConfig = {
  ...defaultTheme?.colors,
  ...defaultTheme?.tokens,
}

export function ThemeProvider<WrapperComponentType>({
  children,
  scope,
  theme: propsTheme,
  WrapperComponent,
  ...wrapperProps
}: ThemeProviderProps<WrapperComponentType>) {
  /**
   * Fallback to default theme on `null` and `undefined`
   */
  const theme = useMemo(() => propsTheme ?? baseTheme, [propsTheme])

  const overrideStyles = [
    reactDatePickerStyleOverride,
    animationStyles,
    zIndexStyles,
  ]

  const cssVariables = parseToken(theme)
  const variableScope = scope ?? ":root"

  const injectorId = `cjx-${getInjectorId(variableScope)}`
  const wrapperId = getThemeWrapperIdentityId(injectorId)
  const hasWrapper = Boolean(scope)
  const cssSelector = hasWrapper
    ? `[data-cjx="${wrapperId}"], ${scope}`
    : ":root"
  const Wrapper = WrapperComponent
    ? typeof WrapperComponent === "string"
      ? (WrapperComponent as keyof ReactHTML)
      : (WrapperComponent as ComponentType)
    : ("div" as keyof ReactHTML)

  return (
    <>
      <style
        dangerouslySetInnerHTML={{
          __html: overrideStyles?.join(""),
        }}
        data-cjx={`${injectorId}-style-override`}
      />
      <style
        dangerouslySetInnerHTML={{
          __html: `${cssSelector} {${cssVariables}}`,
        }}
        data-cjx={`${injectorId}-theme`}
        data-cjx-wrapper-id={wrapperId}
        data-cjxisscope={scope ? "true" : "false"}
      />
      {hasWrapper ? (
        <Wrapper
          data-cjx={wrapperId}
          {...(wrapperProps as WrapperComponentType)}
        >
          {children}
        </Wrapper>
      ) : (
        children
      )}
      <div id="apollo-portal-root" />
    </>
  )
}
