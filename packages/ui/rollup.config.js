import { exec } from "child_process"
import path from "path"
import alias from "@rollup/plugin-alias"
import { babel } from "@rollup/plugin-babel"
import commonjs from "@rollup/plugin-commonjs"
import { nodeResolve } from "@rollup/plugin-node-resolve"
import terser from "@rollup/plugin-terser"
import typescript from "@rollup/plugin-typescript"
import banner2 from "rollup-plugin-banner2"
import peerDepsExternal from "rollup-plugin-peer-deps-external"
import { visualizer } from "rollup-plugin-visualizer"
import postcss from "rollup-plugin-postcss"
import pkg from "./package.json" assert { type: "json" }
import { getFiles } from "./scripts/buildUtils"
import json from "@rollup/plugin-json"
import { generateCssStringModule } from "./generateCssPlugin"

const extensions = [".js", ".ts", ".jsx", ".tsx"]
const excludeExtensions = ["test.js", "test.ts", "test.jsx", "test.tsx"]

// for resolving tsconfig alias path
// https://github.com/ezolenko/rollup-plugin-typescript2/issues/201#issuecomment-1014261983
const tscAlias = () => {
  return {
    name: "tsAlias",
    writeBundle: () => {
      return new Promise((resolve, reject) => {
        exec("tsc-alias", function callback(error, stdout, stderr) {
          if (stderr || error) {
            reject(stderr || error)
          } else {
            resolve(stdout)
          }
        })
      })
    },
  }
}

const outputOptions = {
  sourcemap: false,
  preserveModules: true,
  preserveModulesRoot: "src",
  // inlineDynamicImports: true,
}

const minify = true

const config = [
  {
    input: [
      "src/index.ts",
      "src/styles/globals.css",
      ...getFiles("./src/components", extensions, excludeExtensions),
    ],
    output: [
      {
        dir: "dist",
        format: "cjs",
        entryFileNames: "[name].cjs",
        exports: "named",

        // https://github.com/Lazyuki/rollup-plugin-rename-node-modules?tab=readme-ov-file#why
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name.includes("node_modules")) {
            return chunkInfo.name.replace("node_modules", "external") + ".cjs"
          }

          return "[name].cjs"
        },
        ...outputOptions,
      },
      {
        dir: "dist",
        format: "esm",
        exports: "named",
        entryFileNames: "[name].js",
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name.includes("node_modules")) {
            return chunkInfo.name.replace("node_modules", "external") + ".js"
          }

          return "[name].js"
        },
        ...outputOptions,
      },
    ],

    external: [
      "react",
      "react-dom",
      "react/jsx-runtime",
      "@babel/runtime",
      "@apollo/core",
    ],
    plugins: [
      peerDepsExternal(),
      nodeResolve({ dedupe: ["react"] }),
      json(),
      commonjs(),
      postcss({
        extensions: [".css", ".scss", ".sass", ".less"],
        minimize: minify,
        extract: path.resolve(__dirname, "dist/theme.css"),
        modules: {
          generateScopedName: "[local]___[hash:base64:5]",
        },
        autoModules: true,

        config: {
          path: path.resolve(__dirname, "./postcss.config.cjs"),
          ctx: {
            minify,
          },
        },
      }),
      alias({
        entries: [
          {
            find: "@",
            replacement: path.resolve("./src"),
          },
        ],
      }),
      typescript({
        tsconfig: "./tsconfig.json",
        declaration: true,
        declarationDir: "dist",
      }),
      terser({ compress: { directives: false } }),
      tscAlias(),
      banner2(() => `"use client";\n`),
      // bundleSize(),
      visualizer({
        sourcemap: false,
        filename: `stats/${pkg.name}${minify ? "-min" : ""}.html`,
      }),
      generateCssStringModule(),
    ],
    onwarn(warning, warn) {
      // hide annoying error msgs
      // https://github.com/rollup/rollup/issues/4699#issuecomment-1300284024
      if (warning.code !== "MODULE_LEVEL_DIRECTIVE") {
        warn(warning)
      }
    },
  },
]

export default config
