"use client"

import { useState } from "react"
import { DateInput } from "@design-systems/apollo-ui"

export default function DateInputWithDateRangeDemo() {
  const [startDate, setStartDate] = useState<Date | null>()
  const [endDate, setEndDate] = useState<Date | null>()
  const onChange = (dates: (Date | null)[]) => {
    console.log("[Info] onChange", dates)

    const [start, end] = dates
    setStartDate(start)
    setEndDate(end)
  }

  return (
    <div className="flex flex-col gap-1 justify-center items-center">
      <DateInput
        isRange
        onChange={onChange}
        startDate={startDate}
        endDate={endDate}
        inline
      />
    </div>
  )
}
