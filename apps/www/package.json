{"name": "www", "version": "1.12.0-beta.1", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "contentlayer build && pnpm build:registry && next build", "build:registry": "tsx --tsconfig ./tsconfig.scripts.json ./scripts/build-registry.ts", "seed:tasks": "tsx --tsconfig ./tsconfig.scripts.json ./app/examples/tasks/data/seed.ts", "start": "next start -p 3001", "serve": "serve out", "_lint": "next lint", "_lint:fix": "next lint --fix", "preview": "next build && next start -p 3001", "typecheck": "contentlayer build && tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "test": "echo \"www test...\"", "clean:cache": "rm -rf ./.next", "clean": "rm -rf ./node_modules && rm -rf ./.turbo && rm -rf ./out && rm -rf ./.next && rm -rf ./.contentlayer"}, "dependencies": {"@apollo/ui": "workspace:*", "@design-systems/apollo-icons": "workspace:*", "@faker-js/faker": "^8.2.0", "@google/generative-ai": "^0.16.0", "@hookform/resolvers": "^3.1.0", "@radix-ui/react-accessible-icon": "^1.0.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.4", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.4", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-hover-card": "^1.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.3", "@radix-ui/react-navigation-menu": "^1.1.3", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.6", "@tanstack/react-query": "^5.51.5", "@tanstack/react-table": "^8.9.1", "@types/lodash": "^4.17.0", "@vercel/og": "^0.0.21", "chart.js": "^4.4.5", "class-variance-authority": "^0.4.0", "clsx": "^1.2.1", "cmdk": "^0.2.0", "contentlayer": "0.3.4", "date-fns": "^2.30.0", "embla-carousel-autoplay": "8.0.0-rc15", "embla-carousel-react": "8.0.0-rc15", "flat": "^6.0.1", "geist": "^1.1.0", "jotai": "^2.1.0", "lodash": "^4.17.21", "lucide-react": "0.288.0", "markdown-wasm": "^1.2.0", "next": "14.2.10", "next-contentlayer": "0.3.4", "next-themes": "^0.2.1", "nextjs-toploader": "^1.6.6", "papaparse": "^5.4.1", "react": "^18.2.0", "react-day-picker": "^8.7.1", "react-dom": "^18.2.0", "react-hook-form": "^7.44.2", "react-markdown": "^9.0.1", "react-resizable-panels": "^0.0.55", "react-syntax-highlighter": "^15.5.0", "react-wrap-balancer": "^0.4.1", "recharts": "^2.6.2", "serve": "^14.2.1", "sharp": "^0.32.6", "sonner": "^1.2.3", "tailwind-merge": "^1.12.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.2.0", "zod": "^3.21.4"}, "devDependencies": {"@design-systems/tokens": "workspace:*", "@types/node": "^17.0.45", "@types/react": "^18.2.7", "@types/react-color": "^3.0.6", "@types/react-dom": "^18.2.4", "esbuild": "^0.17.19", "eslint": "^8.41.0", "mdast-util-toc": "^6.1.1", "postcss": "^8.4.24", "react-live": "^4.1.6", "rehype": "^12.0.1", "rehype-autolink-headings": "^6.1.1", "rehype-pretty-code": "^0.6.0", "rehype-slug": "^5.1.0", "remark": "^14.0.3", "remark-code-import": "^1.2.0", "remark-gfm": "^3.0.1", "rimraf": "^4.1.3", "shiki": "^0.12.1", "tailwindcss": "^3.4.0", "tsx": "^4.7.1", "typescript": "^4.9.5", "unist-builder": "3.0.0", "unist-util-visit": "^4.1.2"}, "peerDependencies": {"esbuild": "^0.17.3"}}