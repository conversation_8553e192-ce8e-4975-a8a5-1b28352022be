{"name": "apollo", "version": "1.13.1", "private": true, "scripts": {"adjust-deps-version": "node ./scripts/adjust-deps.js", "adjust-legacy-deps-version": "node ./scripts/adjust-deps.js", "modify": "node ./scripts/modify.js", "reinstall": "pnpm --filter=@design-systems/apollo-ui build && pnpm --filter=www install", "pre-build": "pnpm build:token-new && pnpm build:sandpack", "build": "pnpm pre-build && turbo build --filter={docs,@apollo/ui,@apollo/token,@design-systems/apollo-ui,@apollo/core,@apollo/storefront}", "prepare:build-ui": "cross-env COMMAND=build npm run adjust-deps-version", "build:publish": "cross-env COMMAND=build npm run modify && pnpm --filter=@design-systems/apollo-ui-legacy build", "build:publish-core": "pnpm prepare:build-ui && pnpm --filter=@apollo/core build", "build:publish-new": "pnpm prepare:build-ui && pnpm --filter=@apollo/ui build", "build:publish-storefront": "pnpm prepare:build-ui && pnpm --filter=@apollo/storefront build", "build:publish-legacy": "pnpm prepare:build-ui && pnpm --filter=@design-systems/apollo-ui build", "build:token": "pnpm --filter=@design-systems/tokens build", "build:token-new": "pnpm --filter=@apollo/token build", "build:publish:icons": "pnpm --filter=@design-systems/apollo-icons build", "build:storybook:pages": "pnpm build:publish:icons && pnpm build:token-new && pnpm --filter=@apollo/storefront build && pnpm --filter=@apollo/ui build && pnpm --filter=apollo-docs build-storybook:pages", "dev-legacy": "npm run modify && pnpm run build:token-new && pnpm build:sandpack && turbo dev --parallel --filter={www,labs,@design-systems/apollo-ui,@apollo/ui,docs}", "dev": "npm run modify && pnpm run build:token-new && pnpm build:sandpack && turbo dev --parallel --filter={docs,labs,@apollo/ui,@design-systems/apollo-ui,@apollo/core,@apollo/storefront,apollo-docs}", "dev:pilot": "npm run modify && pnpm run build:token-new && pnpm build:sandpack && turbo dev --parallel --filter={@apollo/ui,@design-systems/apollo-ui,@apollo/pilot}", "release:tag": "node scripts/releaseTag.mjs", "release:publish": "pnpm publish --recursive --tag latest", "release:publish:dry-run": "pnpm publish --recursive --tag latest --registry=\"http://localhost:4873/\"", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:write": "turbo format:write", "format:check": "turbo format:check", "component:dev": "pnpm --filter=@design-systems/apollo-ui dev", "www:dev": "pnpm --filter=www dev", "www:build": "pnpm --filter=www build", "prepare": "husky", "clean": "turbo clean", "test": "turbo test", "test:ui": "pnpm --filter=@design-systems/apollo-ui test", "install:workspace": "pnpm --filter=www install && pnpm --filter=@design-systems/apollo-ui install", "build:sandpack": "pnpm --filter={@design-systems/apollo-ui,@design-systems/apollo-icons,@design-systems/tokens} build:sandpack"}, "workspaces": ["apps/*", "packages/*", "tools/*"], "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.2", "@ianvs/prettier-plugin-sort-imports": "^4.1.1", "@types/fs-extra": "^11.0.4", "@types/yargs": "^17.0.32", "eslint-config-custom": "workspace:*", "fs-extra": "^11.2.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "netlify-cli": "19.0.0", "prettier": "^3.1.1", "turbo": "latest", "yargs": "^17.7.2"}, "packageManager": "pnpm@10.15.1", "engines": {"node": ">=18"}, "dependencies": {"@changesets/cli": "^2.27.1", "cross-env": "^7.0.3"}, "lint-staged": {"./**/*.{js,ts,tsx,mjs}": ["pnpm --filter={@design-systems/apollo-ui,apollo-build-icons} lint --", "pnpm --filter={@design-systems/apollo-ui,apollo-build-icons} lint:fix --"]}, "pnpm": {"onlyBuiltDependencies": ["core-js", "core-js-pure", "es5-ext", "msw", "netlify-cli", "esbuild", "node-jq", "protobufjs", "sharp", "unix-dgram"], "overrides": {"phin@<3.7.1": ">=3.7.1", "braces@<3.0.3": ">=3.0.3", "@grpc/grpc-js@>=1.10.0 <1.10.9": ">=1.10.9", "ws@>=8.0.0 <8.17.1": ">=8.17.1", "micromatch@<4.0.8": ">=4.0.8", "react-is": "19.0.0"}}}